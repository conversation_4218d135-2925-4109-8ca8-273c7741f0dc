/* eslint-disable max-lines */
import styled, { css } from "styled-components";
import { Form, Image, Col, Row } from "react-bootstrap";
import ProgressBar from "react-bootstrap/ProgressBar";
import { apLogo, apLogoShort, apIcon, notFoundLogo, videoMockup, GreenCheckIcon } from "@src/assets";
import {
	faChevronRight,
	faChevronLeft,
	faTimes,
	faArrowRotateRight,
	faArrowRightLong,
	faTrash,
	faStar,
	faEyeDropper
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { InvitationStatus } from "@src/types/invitations";
import { SnippetDisplayMode } from "@src/types/snippetOptions";
import { MetricTypes } from "@src/types/metrics";

const HeaderDiv = styled.div`
	width: 94%;
	margin: 2rem 3%;
	display: inline-block;
`;

const LeftSide = styled.div`
	float: left;
`;

const RightSide = styled.div`
	float: right;
`;

const FlexEnd = styled.div`
	display: flex;
	align-self: flex-end;
`;

const PageBody = styled.div`
	padding: 2% 10% 5% 10%;
`;

const FlexCenter = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
`;

const PageRow = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	display: flex;
	-webkit-box-pack: justify;
	justify-content: space-between;
	align-items: center;
	gap: 1.25rem;

	${(props: {noJustify?: boolean}) =>
		props.noJustify &&
		css`
			-webkit-box-pack: unset;
			justify-content: unset;
		`}
`;

const CompanySection = styled.div`
	max-width: 500px;
	margin: 2rem auto;
	justify-content: center;
	align-items: center;
	text-align: center;
	outline: none;
`;

const FlexCol = styled(Col)`
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
`;

const FullHeightRow = styled(Row)`
	height: 100vh;
`;

const DesktopRow = styled(Row)`
	@media (max-width: 680px) {
		display: none;
	}
`;

const MobileRow = styled(Row)`
	@media (min-width: 681px) {
		display: none;
	}
`;

const EmptySection = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	padding: 15%;
	margin-top: 3rem;
	border-radius: 20px;
	text-align: center;
`;

const OneCollection = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	padding: 5% 15%;
	margin-top: 3rem;
	border-radius: 20px;
	text-align: center;
`;

const VideosSection = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	text-align: center;
`;

const CustomSelect = styled(Form.Select)`
	border: 1px solid ${(props) => props.theme.colors.apInputBackground};
	background-color: ${(props) => props.theme.colors.apInputBackground};
	color: ${(props) => props.theme.colors.apInputColor};
	width: 100%;
	border-radius: 10px;
	padding: 1rem;

	background-image: url("data:image/svg+xml,${encodeURIComponent(
		"<svg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='sort' class='svg-inline--fa fa-sort' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'><path fill='#DEDDDD' d='M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8H32c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8H288c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z'></path></svg>"
	)}") !important;
	background-size: 16px 16px !important;

	&:focus {
		border-color: ${(props) => props.theme.colors.apWhite};
		outline: none;
		box-shadow: none;
	}
`;

const CustomSwitch = styled(Form.Check)`
	display: -webkit-flex; /* Safari */
	-webkit-flex-direction: row-reverse; /* Safari 6.1+ */
	display: flex;
	flex-direction: row-reverse;
	color: ${(props) => props.theme.colors.apTextColor};

	.form-check-input {
		margin-left: 0px;
		margin-right: 10px;
	}

	label {
		margin-right: 10px;
	}

	.form-check-input,
	label {
		cursor: pointer;
		-webkit-user-select: none; /* Safari */
		-ms-user-select: none; /* IE 10 and IE 11 */
		user-select: none;
	}

	.form-check-input,
	.form-check-input:focus {
		outline: 0;
		box-shadow: none;
		background-color: ${(props) => props.theme.colors.apLowMedGrey};
		border: 1px solid ${(props) => props.theme.colors.apLowMedGrey};
		background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%27-4 -4 8 8%27%3e%3ccircle r=%273%27 fill=%27%23fff%27/%3e%3c/svg%3e");
	}

	.form-check-input:checked {
		background-color: ${(props) => props.theme.colors.apSuccess};
		border-color: ${(props) => props.theme.colors.apSuccess};
	}

	.form-check-input:disabled,
	.form-check-input:disabled:checked {
		opacity: 0.5;
		cursor: not-allowed;
	}

	&:has(.form-check-input:disabled) label {
		opacity: 0.5;
		cursor: not-allowed;
	}
`;

const TooltipWrapper = styled.div<{disabled?: boolean}>`
	cursor: ${(props) => (props.disabled ? "not-allowed" : "default")};
`;

const LeftImage = styled(Image)`
	width: 100%;
`;

const DivWithImage = styled.div`
	background-image: url(${videoMockup});
	background-size: cover;
	background-position: center;
	background-color: ${(props) => props.theme.colors.logoBackground};
	border-radius: 10px;
	padding: 15px 30px;
	margin: 20px 0;

	@media only screen and (min-width: 1200px) {
		min-height: 235px;
	}
`;

const ImageBox = styled.div`
	height: 100%;
	background-image: ${(props: {backgroundUrl: string}) => `url(${props.backgroundUrl})`};
	background-size: cover;
	background-position: center;
	background-color: ${(props) => props.theme.colors.apOffBlack};
	@media only screen and (max-width: 767px) {
		height: 30vh;
	}
`;

const ImgContentPanel = styled.div`
	height: max(100vh, 100%);
	@media only screen and (max-width: 767px) {
		height: 30vh;
	}
`;

const ImgContentPanelText = styled.div`
	color: ${(props) => props.theme.colors.apWhite};
	font-family: ${(props) => props.theme.fonts.family};
	bottom: 12%;
	left: 10%;
	max-width: 60%;
`;

const ImgContentPanelTitle = styled.div`
	font-size: 2.2rem;
	line-height: 2.6rem;
	@media only screen and (max-width: 767px) {
		font-size: 1.5rem;
	}
`;

const ImgContentPanelCopy = styled.div`
	@media only screen and (max-width: 1000px) {
		display: none;
	}
`;

const ImageButton = styled.div<{disabled?: boolean, clickable?: boolean}>`
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 7px;
	// padding: 0.7rem 1rem;
	width: 50px;
	height: 45px;
	margin-right: 15px;
	cursor: ${(props) => (props.clickable ?? true) ? "pointer" : "not-allowed"};
	display: flex;
	justify-content: center;
	align-items: center;
	line-height: 0;
	
	&:hover {
		background-color: ${(props) => props.theme.colors.apGreyButtonHover};
	}

	${(props) =>
		props.disabled &&
		css`
			iframe {
				opacity: 0.3;
			}
			background-color: ${(props) => props.theme.colors.apSectionBackground};
			&:hover {
				background-color: ${(props) => props.theme.colors.apSectionBackground};
			}
		`}

	.lockImage {
		position: absolute;
		width: 8px !important;
		margin-top: -22.5px;
		margin-right: -25px;
		opacity: 1;
		fill: ${(props) => props.theme.colors.apGreyButton} !important;
	}
`;

const MainButton = styled.button<{resize?: boolean; reverse?: boolean; greyColor?: boolean; inactive?: boolean}>`
	border: 0;
	background-color: ${(props) => props.theme.colors.apButton};
	color: ${(props) => props.theme.colors.apButtonColor};
	fill: ${(props) => props.theme.colors.apButtonColor};
	font-family: ${(props) => props.theme.fonts.family};
	border-radius: 7px;
	padding: 1rem 2rem;
	font-size: 1rem;
	width: fit-content;
	font-weight: bold;
	-webkit-user-select: none;
	-ms-user-select: none;
	user-select: none;

	&:hover {
		background-color: ${(props) => props.theme.colors.apButtonHover};
	}
	&:active {
		background-color: ${(props) => props.theme.colors.apButtonActive};
		box-shadow: inset 0px 3px 6px ${(props) => props.theme.colors.apButtonShadow};
	}
	&:disabled {
		background-color: ${(props) => props.theme.colors.apButtonActive};
		box-shadow: inset 0px 3px 6px ${(props) => props.theme.colors.apButtonShadow};
		opacity: 0.5;
	}

	${(props) =>
		props.inactive &&
		css`
			background-color: ${(props) => props.theme.colors.apButtonActive};
			box-shadow: inset 0px 3px 6px ${(props) => props.theme.colors.apButtonShadow};
			opacity: 0.5;
		`}

	${(props) =>
		props.reverse &&
		css`
			background-color: ${(props) => props.theme.colors.apBackgroundColor};
			color: ${(props) => props.theme.colors.apTextColor};
			fill: ${(props) => props.theme.colors.apTextColor};
			&:hover {
				background-color: ${(props) => props.theme.colors.apBackgroundColor};
				opacity: 0.9;
			}
			&:active {
				background-color: ${(props) => props.theme.colors.apBackgroundColor};
				opacity: 0.9;
			}
		`}

	${(props) =>
		props.greyColor &&
		css`
			background-color: ${(props) => props.theme.colors.apSectionBackground};
			border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
			color: ${(props) => props.theme.colors.apTextColor};
			fill: ${(props) => props.theme.colors.apTextColor};
			&:hover {
				background-color: ${(props) => props.theme.colors.apSectionBackground};
				opacity: 0.7;
			}
			&:active {
				background-color: ${(props) => props.theme.colors.apSectionBackground};
				box-shadow: none;
				opacity: 0.7;
			}
			&:disabled {
				background-color: ${(props) => props.theme.colors.apSectionBackground};
				box-shadow: none;
				opacity: 0.5;
			}
		`}

	@media only screen and (max-width: 1099px) and (min-width: 768px) {
		${(props) =>
			props.resize &&
			`font-size: 0.9rem;
				padding: 0.8rem 1.6rem;
			`}
	}

	@media only screen and (max-width: 875px) and (min-width: 768px) {
		${(props) =>
			props.resize &&
			`font-size: 0.9rem;
			padding: 0.5rem 1rem;
		`}
	}
`;

const MainSuccessButton = styled(MainButton)`
	border: 1px solid ${(props) => props.theme.colors.apSuccess};
	background-color: ${(props) => props.theme.colors.apSuccess};
	&:hover {
		background-color: ${(props) => props.theme.colors.apSuccess};
	}
	&:active {
		background-color: ${(props) => props.theme.colors.apSuccess};
		box-shadow: unset;
	}
	&:disabled {
		background-color: ${(props) => props.theme.colors.apSuccess};
		box-shadow: unset;
		opacity: 0.5;
	}
`;

const GreyButton = styled.button`
	background-color: ${(props) => props.theme.colors.apGreyButton};
	border: 1px solid ${(props) => props.theme.colors.apGreyButton};
	color: ${(props) => props.theme.colors.apGreyButtonColor};
	fill: ${(props) => props.theme.colors.apGreyButtonColor};
	font-family: ${(props) => props.theme.fonts.family};
	border-radius: 30px;
	margin: 5px 0px;
	padding: 0px 15px;
	font-size: 0.9rem;
	font-weight: bold;

	&:hover {
		background-color: ${(props) => props.theme.colors.apGreyButtonHover};
	}
	&:active {
		background-color: ${(props) => props.theme.colors.apGreyButtonActive};
	}
	&:disabled {
		opacity: 0.5;

		&:hover {
			background-color: ${(props) => props.theme.colors.apGreyButton};
		}
	}
`;

const ThirdButton = styled.button<{greyColor?: boolean}>`
	background-color: ${(props) => props.theme.colors.apThirdButton};
	color: ${(props) => props.theme.colors.apThirdButtonColor};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-size: 1rem;
	width: fit-content;
	font-weight: bold;

	&:hover {
		color: ${(props) => props.theme.colors.apThirdButtonHover};
	}
	&:active {
		color: ${(props) => props.theme.colors.apThirdButtonActive};
	}

	${(props) =>
		props.greyColor &&
		css`
			color: ${(props) => props.theme.colors.apOffBlack};

			&:hover {
				color: ${(props) => props.theme.colors.apThirdButtonActive};
			}
			&:active {
				color: ${(props) => props.theme.colors.apThirdButtonActive};
			}
		`}
`;

const ProLinkButton = styled.p<{basic?: boolean, clickable?: boolean}>`
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	color: ${(props) => props.theme.colors.apTextColor};
	font-family: ${(props) => props.theme.fonts.family};
	border-radius: 4px;
	padding: 5px 10px;
	font-weight: bold;
	font-size: 14px;
	cursor: ${(props) => (props.clickable ?? true) ? "pointer" : "unset"};
	text-decoration: none;
	margin-right: 1rem;
	display: inline;
`;

const ProIconHeader = styled.img`
	max-width: 16px;
	vertical-align: sub;
	margin-right: 10px;
`;

const UploadIconImg = styled.img`
	width: 30px;
	height: 30px;
	margin-bottom: 10px;
`;

const UploadIconDiv = styled.div`
	width: 30px;
	height: 30px;
	margin-bottom: 10px;
	margin-left: auto;
	margin-right: auto;
	fill: ${(props) => props.theme.colors.apTextColor};
`;

const PhotoSample = styled.img`
	width: 100%;
	height: auto;
	object-fit: contain;
	margin-bottom: 10px;
`;

const TextImage = styled.img`
	vertical-align: bottom;
	width: 25px;
	height: 25px;
	margin-right: 1rem;
`;

const RecordingTime = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	text-align: center;
	font-weight: bold;
	font-size: 1.2rem;
	margin: 1rem 0;
`;

const RecordingText = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	height: 100%;
	border-radius: 10px;
	text-align: left;
	padding: 1rem;
	overflow-y: auto;
	overflow-x: hidden;
	max-height: 300px;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	margin-top: 1rem;
	margin-bottom: 1rem;
	word-break: break-word;
	box-sizing: border-box;
	white-space: pre-line;

	::-webkit-scrollbar {
		width: 5px; 
		height: 10px;
	}
	::-webkit-scrollbar-track {
		background-color: ${(props) => props.theme.colors.apSectionBackground};
	}
	::-webkit-scrollbar-thumb {
		background-color: ${(props) => props.theme.colors.apBlack};
		border-radius: 5px;
	}
`;

const FlexGrid = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10px;
`;

const LoadingBar = styled.div`
	flex: 1; 
	height: 5px;
	background: ${(props) => props.theme.colors.loadingBarBackgroundColor};
	border-radius: 2px; 
	position: relative;
`;

const LoadingBarProgress = styled.div`
	position: absolute;
	left: 0;
	top: 0;
	height: 5px;
	background: ${(props) => props.theme.colors.loadingBarColor};
	border-radius: 2px;
`;

const CharactersLimit = styled.div`
	float: right;
	font-size: 0.85rem;
	color: ${(props) => props.theme.colors.apFooterTextColor};
	font-family: ${(props) => props.theme.fonts.family};
`;

const LimitReached = styled.span`
	color: ${(props) => props.theme.colors.apMaxImpressions};
`;

const StyledProgressBar = styled(ProgressBar)`
	margin: .5rem 25% 0 25%; 
	height: 12px;
	border-radius: 20px;
	background-color: ${(props) => props.theme.colors.apProgressBarBGColor};

	.progress-bar {
		background-color: ${(props) => props.theme.colors.apWhite};
		border-radius: 20px;
		transition: width 0.4s ease;
	}
`;

const LinkButton = styled.a`
	color: ${(props) => props.theme.colors.apThirdButtonColor};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-weight: bold;
	font-size: 1rem;
	cursor: pointer;
	text-decoration: none;

	&:hover {
		color: ${(props) => props.theme.colors.apThirdButtonHover};
	}
	&:active {
		color: ${(props) => props.theme.colors.apThirdButtonActive};
	}
`;

const ImpressionsNumbers = styled.div<{"data-max"?: boolean}>`
	color: ${(props) => (props["data-max"] ? props.theme.colors.apMaxImpressions : props.theme.colors.apTextColor)};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-size: 1.2rem;
`;

const BlueTextLink = styled.a`
	color: ${(props) => props.theme.colors.apTextColor};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-size: 1rem;
	cursor: pointer;
	text-decoration: none;
	font-weight: bold;

	&:hover,
	&:active {
		color: ${(props) => props.theme.colors.apThirdButtonHover};
	}
`;

const WhiteTextLink = styled.a`
	color: ${(props) => props.theme.colors.apWhite};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-size: 1rem;
	cursor: pointer;
	text-decoration: none;

	&:hover,
	&:active {
		color: ${(props) => props.theme.colors.apWhite};
	}
`;

const TextLink = styled.a`
	background-color: ${(props) => props.theme.colors.apThirdButton};
	color: ${(props) => props.theme.colors.apButtonHover};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-weight: bold;
	font-size: 1rem;
	cursor: pointer;
	text-decoration: underline;

	&:hover {
		color: ${(props) => props.theme.colors.apButtonActive};
	}
	&:active {
		color: ${(props) => props.theme.colors.apButtonActive};
	}
`;

const CancelPlan = styled.a`
	background-color: ${(props) => props.theme.colors.apThirdButton};
	color: ${(props) => props.theme.colors.apBlack};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-size: 1rem;
	cursor: pointer;
	text-decoration: underline;
	font-weight: bold;
	display: block;
	margin-top: 1.5rem;

	&:hover {
		color: ${(props) => props.theme.colors.apThirdButtonActive};
	}
	&:active {
		color: ${(props) => props.theme.colors.apThirdButtonActive};
	}
`;

const LinkSpan = styled.span<{disabled?: boolean}>`
	background-color: ${(props) => props.theme.colors.apThirdButton};
	color: ${(props) => props.theme.colors.apThirdButtonColor};
	font-family: ${(props) => props.theme.fonts.family};
	border: none;
	font-weight: bold;
	font-size: 1rem;
	cursor: pointer;

	&:hover {
		color: ${(props) => props.theme.colors.apThirdButtonHover};
	}
	&:active {
		color: ${(props) => props.theme.colors.apThirdButtonActive};
	}

	${(props) =>
		props.disabled &&
		css`
			cursor: default;
			opacity: 0.5;
			&:hover {
				color: ${(props) => props.theme.colors.apThirdButtonColor};
			}
			&:active {
				color: ${(props) => props.theme.colors.apThirdButtonColor};
			}
		`}
`;

const AlertLinkSpan = styled.span`
	color: ${(props) => props.theme.colors.apThirdButton};
	border: none;
	font-weight: bold;
	cursor: pointer;
	text-decoration: underline;
`;

const Flex = styled.div`
	display: flex;
`;

const FlexSpace = styled.div`
	display: flex;
	justify-content: space-between;
`;

const FlexColor = styled.div`
	display: flex;
	justify-content: space-between;
	border-radius: 10px;
	font-family: ${(props) => props.theme.fonts.family};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	padding: 15px;
`;

const CheckboxList = styled.div`
	max-height: 300px;
	overflow: hidden;
	overflow-y: scroll;
`;

const FormText = styled.div`
	color: ${(props) => props.theme.colors.apFooterTextColor};
`;

const FlexCheckbox = styled.label`
	display: flex;
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	margin-bottom: 10px;
	font-size: 1rem;
	padding: 15px;
	cursor: pointer;
	align-items: center;
`;

const ListBox = styled(Flex)<{last?: boolean; clickable?: boolean}>`
	padding: 5px;

	${(props) =>
		props.clickable &&
		css`
			cursor: pointer;
		`}

	${(props) =>
		props.last &&
		css`
			justify-content: flex-end;
		`}
`;

const ListGridBox = styled(ListBox)`
	width: 2rem;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	padding-left: 15px;
`;

const HeaderListTitle = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	text-align: left;
	font-weight: 700;
	padding: 15px 7.5px;
`;

const ListItemContainer = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	margin-bottom: 20px;
`;

const ListRowFlex = styled(Flex)`
	flex-wrap: wrap;
	width: calc(1200px + 4rem);
	margin: auto;
	margin-top: 1.5rem;

	@media (max-width: 1580px) {
		width: calc(900px + 3rem);
	}

	@media (max-width: 1180px) {
		width: calc(600px + 2rem);
	}

	@media (max-width: 790px) {
		width: calc(300px + 1rem);
	}
`;

const ListRowItem = styled.div<{disabled?: boolean; noPadding?: boolean}>`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	padding: ${(props) => (props.noPadding ? "0px" : "15px")};
	word-break: normal;
	overflow-wrap: anywhere;

	${(props) =>
		props.disabled &&
		css`
			color: ${(props) => props.theme.colors.disabledTextColor};
		`}
`;

const ListRowGridItem = styled(ListRowItem)`
	display: grid;
	grid-template-columns: repeat(2, 5px);
	grid-template-rows: repeat(3, 5px);
	grid-gap: 2px;
	margin: 0;
	padding: 0;
`;

const ListRowGridItemBox = styled.div`
	background-color: ${(props) => props.theme.colors.apGreyButtonHover};
	width: 5px;
	height: 5px;
	margin: 0;
	padding: 0;
`;

const UserListStatus = styled.div<{status?: string}>`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apWhite};
	font-size: 1rem;
	padding: 3px 8px;
	border-radius: 3px;
	height: fit-content;
	position: relative;
	top: 50%;
	margin-left: 15px;
	transform: translateY(-50%);
	text-transform: capitalize;

	${(props) =>
		props.status === InvitationStatus.OWNER &&
		css`
			background-color: ${(props) => props.theme.colors.apButton};
		`}

	${(props) =>
		props.status === InvitationStatus.ACTIVE &&
		css`
			background-color: ${(props) => props.theme.colors.apSuccess};
		`}
	${(props) =>
		props.status === InvitationStatus.PENDING &&
		css`
			background-color: ${(props) => props.theme.colors.apThirdButtonActive};
		`}
`;

const VideoPageTitleText = styled.div<{resize?: boolean}>`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	padding: 0 0.5rem;
	font-weight: 700;
	margin-bottom: 2rem;

	@media only screen and (max-width: 1099px) and (min-width: 768px) {
		${(props) => props.resize && "font-size: 0.9rem;"}
	}
`;

const TitleText = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	font-weight: 700;
`;

const HomePageTitle = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1.4rem;

	${(props: {whiteColor?: boolean}) =>
		props.whiteColor &&
		css`
			color: ${(props) => props.theme.colors.apWhite};
		`}
`;

const SpanColor = styled.span`
	color: ${(props) => props.theme.colors.apSpanColor};
`;

const SpanGrey = styled.span`
	color: ${(props) => props.theme.colors.apOffBlack};
`;

const CodeSnippetModal = styled.div`
	width: 100%;
	border-radius: 15px;
	padding: 15px;
	display: flex;
	height: auto;
	background: ${(props) => props.theme.colors.snippetModalBackground};
	color: ${(props) => props.theme.colors.snippetModalTextColor};
	letter-spacing: 1px;
	font-weight: 300;
	max-height: 250px;
	overflow-y: scroll;
	overflow-x: hidden;
	-ms-overflow-style: none;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}
`;

const CodeSnippetDiv = styled.div`
	background: ${(props) => props.theme.colors.apInputBackground};
	border-radius: 10px;
	padding: 18px;
`;

const CodeSnippet = styled(CodeSnippetModal)`
	background: ${(props) => props.theme.colors.apInputBackground};
	border-radius: 10px;
	padding: 18px;
`;

const SnippetModalText = styled.span`
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	margin-top: 1rem;
	margin-bottom: 1rem;
`;

const CodeSnippetContainer = styled.div`
	width: 100%;
	border-radius: 15px;
	padding: 15px;
	display: flex;
	height: auto;
	min-height: 150px;
	background: ${(props) => props.theme.colors.snippetBackground};
	border: 1px solid ${(props) => props.theme.colors.snippetBorder};
	color: ${(props) => props.theme.colors.snippetModalTextColor};
	letter-spacing: 1px;
	font-weight: 300;
	max-height: 250px;
	overflow-y: scroll;
	overflow-x: hidden;
	-ms-overflow-style: none;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}
`;

const CustomHighlight = styled.pre`
	white-space: break-spaces;
	max-height: 220px;
	overflow-y: scroll;
	overflow-x: hidden;
	-ms-overflow-style: none;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}
`;

const HighlightCodeModal = styled.code`
	background: ${(props) => props.theme.colors.snippetModalBackground};
`;

const HighlightCode = styled.code`
	background: ${(props) => props.theme.colors.snippetBackground};
	padding: 0.5em !important;
`;

const FileDropBox = styled.div`
	width: 100%;
	height: 530px;
	justify-content: center;
	align-items: center;
	text-align: center;
	outline: none;
	cursor: pointer;
`;

const TableBox = styled.div`
	display: table;
	width: 100%;
	height: 100%;
`;

const NoFileBox = styled.div`
	display: table-cell;
	vertical-align: middle;
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;

	@media only screen and (max-width: 1099px) and (min-width: 768px) {
		vertical-align: top;
		padding-top: 3rem;
	}

	@media only screen and (max-width: 975px) and (min-width: 768px) {
		vertical-align: top;
		padding-top: 1rem;
	}
`;

const ProductsBox = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	padding: 15px;
`;

const VideoSectionModal = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	padding: 15px;
	margin: 20px 0;
`;

const UploadBox = styled.div`
	text-align: center;

	&:hover {
		filter: brightness(0.95);
	}
`;

const CenterDiv = styled.div`
	text-align: center;
	margin-top: 1rem;
`;

const CoverSectionModal = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	padding: 15px;
	margin: 20px 0;
	text-align: center;

	@media only screen and (min-width: 767px) {
		width: 35rem;
	}
`;

const CaptionsSectionModal = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	padding: 15px;
	text-align: center;

	@media only screen and (min-width: 767px) {
		min-width: 28rem;
		max-width: 50rem;
	}
`;

const CaptionsRightColumn = styled.div`
	color: ${(props) => props.theme.colors.apTextColor};

	@media only screen and (min-width: 767px) {
		max-width: 15rem;
	}
`;

const CaptionsBox = styled.div`
	position: absolute;
	left: 50%;
	bottom: 5%;
	transform: translateX(-50%); 
	width: max-content;
	max-width: calc(100% - 1em);
	height: fit-content;
	text-align: center;
	display: block;
	border-radius: 0.5em;
	word-break: break-word;
	box-sizing: border-box;
	white-space: pre-line;
`;

const EditCaptionsArea = styled.textarea`
	width: 100%;
	padding: 10px;
	font-size: 14px;
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apInputColor};
	&:disabled {
		background-color: ${(props) => props.theme.colors.apSectionBackground};
	}
`;

const VideoCaptionsText = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	font-weight: 700;
	display: flex;
	align-items:center;
	justify-content: center;

	flex-direction: row;
	gap: 0.5rem;
`;

const LinkSectionModal = styled.div`
	border-radius: 20px;
	padding: 15px;
	margin: 20px 0;
	text-align: center;

	@media only screen and (min-width: 767px) {
		width: 35rem;
		margin: 20px auto;
	}
`;

const PreviewGif = styled.img`
	width: auto;
	max-width: 90%;
	max-height: 200px;
	border-radius: 7px;
`;

const TabTitleImg = styled.div`
	width: 20px;
	height: 20px;
	margin-right: 5px;
	margin-top: -3px;
	display: inline-block;
	vertical-align: middle;
	fill: ${(props) => props.theme.colors.snippetModalTextColor};

	${(props: {show?: boolean}) =>
		props.show &&
		css`
			fill: ${(props) => props.theme.colors.apButton};
		`}
`;

const ButtonIconImg = styled.img`
	width: 20px;
	height: 20px;
	margin-right: 5px;
	margin-top: -3px;
	cursor: pointer;
`;

const ButtonIconDiv = styled.div`
	width: 20px;
	height: 20px;
	margin-right: 5px;
	margin-top: -3px;
	display: inline-block;
	vertical-align: middle;
	cursor: pointer;
`;

const PreviewImageBox = styled.div`
	position: relative;
	width: fit-content;
	margin: auto;
`;

const VoiceUploadBox = styled.div`
	position: relative;
	width: fit-content;
	margin: auto;
	background: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
	border-radius: 10px;
	padding: 10px 15px;
`;

const TabTitle = styled.div`
	color: ${(props) => props.theme.colors.apFooterTextColor};
	cursor: pointer;
	border-radius: 5px;
	padding: 10px;
	margin-top: 10px;
	text-align: center;

	${(props: {show?: boolean}) =>
		props.show &&
		css`
			color: ${(props) => props.theme.colors.apButton};
			background: ${(props) => props.theme.colors.apSectionBackground};
		`}

	${(props: {show?: boolean}) =>
		!props.show &&
		css`
			&:hover {
				background: ${(props) => props.theme.colors.apSectionBackground};
			}
		`}
`;

const LearnMoreDiv = styled.div`
	background: ${(props) => props.theme.colors.apBlack};
	color: ${(props) => props.theme.colors.apWhite};
	border-radius: 10px;
	padding: 15px 30px;
	margin-top: 20px;
`;

const VideoManager = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	padding: 15px 30px;
	margin: 20px 0;

	@media only screen and (min-width: 768px) {
		min-height: 235px;
	}
`;

const ProductImage = styled.label`
	background: ${(props) => props.theme.colors.apButton};
	color: ${(props) => props.theme.colors.apFormText};
	text-align: center;
	border-radius: 10px;
	min-width: 58px;
	overflow: hidden;
	cursor: pointer;
	height: 58px;
`;

const CustomInputBox = styled.div`
	width: 100%;
`;

const NoImageFileBox = styled.div`
	display: flex;
	vertical-align: middle;
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	margin-bottom: 1rem;
	cursor: pointer;
	align-items: center;
`;

const LogoBox = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	text-align: center;
	padding: 20px 5px;
	cursor: pointer;
`;

const LogoImageBox = styled.div`
	margin: 10px auto 20px auto;
	max-width: 200px;
	max-height: 200px;
	overflow: hidden;
	justify-content: center;
	align-items: center;
	text-align: center;
	outline: none;
	border-radius: 5px;
`;

const CompanyLogo = styled.img`
	background: ${(props) => props.theme.colors.apSectionBackground};
	object-fit: cover;
	object-position: center;
	max-width: 200px;
	max-height: 200px;
	border-radius: 5px;
`;

const CompanyImageHeader = styled.img`
	background: ${(props) => props.theme.colors.apSectionBackground};
	object-fit: cover;
	object-position: center;
	max-width: 30px;
	max-height: 30px;
	border-radius: 5px;
`;

const ApLogo = styled.img`
	max-width: 200px;
	max-height: 52px;
	margin-top: 1.5rem;
	margin-left: 1.5rem;
	position: absolute;
	content: url(${apLogo});
	${(props: {iconOnly?: boolean}) =>
		props.iconOnly &&
		css`
			content: url(${apIcon});
		`}

	@media only screen and (max-width: 1000px) {
		content: url(${apIcon});
	}
`;

const ApNavLogo = styled.img<{isExpanded: boolean; arrowClicked: boolean}>`
	width: ${(props) => (props.isExpanded ? "95px" : "30px")};
	content: ${(props) => (props.isExpanded ? `url(${apLogoShort})` : `url(${apIcon})`)};

	@media only screen and (max-width: 767px) {
		width: ${(props) => (props.arrowClicked ? "95px" : "30px")};
		content: ${(props) => (props.arrowClicked ? `url(${apLogoShort})` : `url(${apIcon})`)};
		margin-left: 0.9rem;
		margin-top: 2rem;
	}
`;

const Ap404Logo = styled.img`
	max-width: 200px;
	max-height: 125px;
	content: url(${notFoundLogo});
`;

const CoverImageBox = styled.div<{landscape?: boolean}>`
	width: 100%;
	height: auto;
	margin: auto;
	overflow: hidden;
	background: ${(props) => props.theme.colors.apWhite};
	justify-content: center;
	align-items: center;
	text-align: center;
	outline: none;
	border-radius: 3px;
	max-width: 26px;
	max-height: 46px;
	aspect-ratio: 9/16;

	${(props) =>
		props.landscape &&
		css`
			max-width: 46px;
			max-height: 26px;
		`}
`;

const UploadImage = styled.img`
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 3px;
`;

const UploadProductImage = styled.img`
	background: ${(props) => props.theme.colors.apSectionBackground};
	object-fit: contain;
	object-position: center;
	width: 58px;
	height: 58px;
`;

const InitialsBox = styled.div`
	text-transform: uppercase;
	margin: auto;
	font-weight: bold;
	background: ${(props) => props.theme.colors.logoBackground};
	color: ${(props) => props.theme.colors.apWhite};
	padding-top: 8px;
	width: 40px;
	height: 40px;
	border-radius: 5px;
	text-align: center;
`;

const CompanyImage = styled.img`
	object-fit: contain;
	object-position: center;
	width: 40px;
	height: 40px;
	border-radius: 5px;
	margin: auto;
`;

const ImageCollections = styled.img`
	width: 100%;
	max-width: 450px;
`;

const ImageCarousel = styled.img`
	width: 100%;
	max-width: 450px;
	position: relative;
	top: 50%;
	transform: translateY(-50%);
`;

const WhiteBoxImage = styled.img`
	padding: 10px;
	width: 90%;
`;

const WhiteBox = styled.div`
	background: ${(props) => props.theme.colors.apWhite};
	border: 2px solid ${(props) => props.theme.colors.apWhite};
	padding: 10px;
	border-radius: 20px;
	margin: 10px 0;
	cursor: pointer;

	&:hover {
		border: 2px solid ${(props) => props.theme.colors.apButton};
	}
`;

const VideoSectionBox = styled.div<{landscape?: boolean, video?: boolean}>`
	border-radius: 20px;
	width: 100%;
	height: auto;
	margin: auto;
	position: relative;
	margin-bottom: 10px;
	aspect-ratio: 9/16;
	max-width: 150px;

	${(props) =>
		props.video &&
		css`
			max-width: 330px;
		`}

	${(props) =>
		props.landscape &&
		css`
			aspect-ratio: 16/9;
			max-width: 350px;
		`}

	${(props) =>
		props.video && props.landscape &&
		css`
			max-width: 90%;
		`}
`;

const VideoPlayer = styled.video`
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 20px;
`;

const UploadImagePreview = styled.img`
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 20px;
`;

const ProgressBarTime = styled.div`
	color: ${(props) => props.theme.colors.apFooterTextColor};
	user-select: none;
`;

const ProgressBarCustom = styled.div`
	width: 100%;
	background-color: ${(props) => props.theme.colors.apFooterTextColor};
	height: 5px;
	border-radius: 5px;
	position: relative;
	margin: 10px;
`;

const ThemedProgressBar = styled.div`
	.bg-bar-color {
		background-color: ${(props) => props.theme.colors.apSpanColor};
	}
`;

const CanvasImage = styled.canvas<{landscape?: boolean}>`
	width: 25px;
	height: 40px;
	margin-left: -10px;
	margin-top: -17px;
	border-radius: 5px;
	cursor: grab;
	position: absolute;
	object-fit: cover;

	${(props) =>
		props.landscape &&
		css`
			width: 40px;
			height: 25px;
			margin-left: -15px;
			margin-top: -10px;
		`}

	&:active {
		cursor: grabbing;
	}
`;

const VideoPlayerText = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apOffBlack};
	font-weight: 700;
	font-size: 1rem;
`;

const HttpErrorContainer = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100vh;
	max-width: 30rem;
	margin: auto;
	padding: 2rem;
	font-family: ${(props) => props.theme.fonts.family};
`;

const HttpErrorCode = styled.h1`
	font-weight: bold;
	font-size: 3rem;
	color: ${(props) => props.theme.colors.apButton};
`;

const HttpErrorTitle = styled.h2`
	font-weight: bold;
`;

const HttpErrorMessage = styled.div`
	text-align: center;
	font-size: 0.9rem;
`;

const SidebarContainer = styled.div<{isExpanded: boolean; arrowClicked: boolean}>`
	width: ${(props) => (props.isExpanded ? "225px" : "56px")};
	transition: width 0.25s;
	background: ${(props) => props.theme.colors.apButton};
	position: fixed;
	height: 100vh;
	top: 0;
	z-index: 99999999;
	overflow: hidden;
	font-family: ${(props) => props.theme.fonts.family};

	.nav-item {
		text-decoration: none;
	}

	.nav-close {
		display: none;
	}

	@media only screen and (max-width: 767px) {
		width: ${(props) => (props.arrowClicked ? "100%" : "0px")};
		height: 100%;

		.nav-close {
			display: block;
			color: ${(props) => props.theme.colors.apWhite};
			text-decoration: none;
			position: absolute;
			top: 1rem;
			left: calc(100vw - 4rem);
			font-size: 2rem;
			cursor: pointer;
		}
	}
`;

const StyledNavItem = styled.div<{isExpanded: boolean; arrowClicked: boolean; noHover?: boolean}>`
	display: flex;
	align-items: center;
	padding: 20px 20px;
	-moz-user-select: none;
	-webkit-user-select: none;
	user-select: none;
	cursor: ${(props) => (props.noHover ? "default" : "pointer")};

	.bold {
		font-weight: bold;
	}

	.nav-text {
		color: ${(props) => props.theme.colors.apButtonColor};
		opacity: ${(props) => (props.isExpanded ? "1" : "0")};
		transition: opacity 0.5s;
		margin-left: 46px;
		position: absolute;
		width: 200px;
	}

	&:hover {
		background: ${(props) => (props.noHover ? props.theme.colors.apButton : props.theme.colors.apButtonHover)};
	}

	@media only screen and (max-width: 767px) {
		padding: 20px 2rem;
		font-size: 1.2rem;

		.nav-text {
			opacity: ${(props) => (props.arrowClicked ? "1" : "0")};
		}
	}
`;

const PlanItemBtn = styled.div<{pro?: boolean; noHover?: boolean}>`
	font-family: "Readex Pro", sans-serif;
	display: flex;
	align-items: center;
	font-weight: bold;
	border-radius: 10px;
	padding: 20px;

	cursor: ${(props) => (props.noHover ? "default" : "pointer")};
	color: ${(props) => (props.pro ? props.theme.colors.apWhite : props.theme.colors.apBlack)};
	background: ${(props) => (props.pro ? props.theme.colors.apButton : props.theme.colors.apSectionBackground)};

	span {
		margin-left: 10px;
	}

	&:hover {
		background: ${(props) => (props.pro && !props.noHover ? props.theme.colors.apButtonHover : "")};
	}
`;

const BtnIcon = styled.img`
	width: 16px;
	height: 16px;
`;

const NavIcon = styled.div`
	fill: ${(props) => props.theme.colors.apButtonColor};
	color: ${(props) => props.theme.colors.apButtonColor};
	height: 1rem;
	width: 1rem;
	scale: 1.5;
	line-height: 0;
	display: inline-block;
	vertical-align: middle;
`;

const SidebarCircleContainer = styled.div<{isExpanded: boolean; arrowClicked: boolean}>`
	position: fixed;
	width: 50px;
	top: 10px;
	left: ${(props) => (props.isExpanded ? "217px" : "48px")};
	transition: left 0.25s;

	@media only screen and (max-width: 767px) {
		left: ${(props) => (props.arrowClicked ? "100vw" : "-8px")};
		cursor: pointer;
	}
`;

const MetricIcon = styled.div`
	fill: ${(props) => props.theme.colors.apGreyButtonHover};
	color: ${(props) => props.theme.colors.apGreyButtonHover};
	float: right;
	line-height: 0;
	width: 17px;
	height: 17px;
`;

const InlineMetricIcon = styled.div`
	fill: ${(props) => props.theme.colors.apTextColor};
	color: ${(props) => props.theme.colors.apTextColor};
	height: 1em;
	width: 1em;
	line-height: 0;
	display: inline-block;
	vertical-align: middle;
`;

const InlineIcon = styled.div`
	fill: ${(props) => props.theme.colors.apTextColor};
	color: ${(props) => props.theme.colors.apTextColor};
	height: 100%;
	width: 16px;
	line-height: 0;
	display: inline-block;
	vertical-align: middle;
`;

const SidebarNotch = styled.div`
	color: ${(props) => props.theme.colors.apButton};
	fill: ${(props) => props.theme.colors.apButton};
	width: 50px;
	height: 128.5px;
`;

const ChevronRight = styled.div`
	font-size: 0.67rem;
	color: ${(props) => props.theme.colors.apButtonColor};
	position: absolute;
	left: 16px;
	top: 60px;
	height:10.72px;
	line-height: 0;
	display: inline-block;
	vertical-align: middle;
`;

const InvitePending = styled.div`
	font-size: 0.67rem;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apFooterTextColor};
	text-align: left;
`;

const InviteActions = styled.div`
	display: flex;
`;

const FetchingProductText = styled.div`
	height: 58px;
	display: flex;
	align-items: center;
	justify-content: center;
`;

const SelectItem = styled.div<{readonly?: boolean}>`
	font-family: ${(props) => props.theme.fonts.family};
	background-color: ${(props) => props.theme.colors.apInputBackground};
	color: ${(props) => (props.readonly ? props.theme.colors.disabledTextColor : props.theme.colors.apInputColor)};
	height: 88px;
	width: 100%;
	display: flex;
	border-radius: 10px;
	margin-top: 1rem;
	justify-content: space-between;
	align-items: center;
	padding: 1rem;
`;

const SelectItemTitle = styled.div`
	font-weight: 700;
`;

const SelectItemContainer = styled.div`
	display: flex;
`;

const SelectItemSubtext = styled.div`
	font-size: 0.8rem;
`;

const RemoveIcon = styled(FontAwesomeIcon).attrs({ icon: faTimes })`
	color: ${(props) => props.theme.colors.apError};
	font-size: 1.2rem;
	margin-left: 1rem;
	cursor: pointer;
`;

const MockImage = styled.img<{imageURL: string; displayTypeValue: SnippetDisplayMode}>`
	content: url(${(props) => props.imageURL});
	width: ${(props) => {
		if (props.displayTypeValue === SnippetDisplayMode.ALL) return "12.5rem";
		if (props.displayTypeValue === SnippetDisplayMode.DOUBLE) return "14rem";
		if (props.displayTypeValue === SnippetDisplayMode.SM_DOUBLE || props.displayTypeValue === SnippetDisplayMode.SINGLE)
			return "10rem";
	}};
`;

const MockImageLarge = styled.img<{imageURL: string; displayTypeValue: SnippetDisplayMode}>`
	content: url(${(props) => props.imageURL});
	width: 20rem;
	margin-left: 1rem;
`;

const ImageSelectContainer = styled.div<{selected?: boolean}>`
	display: inline-block;
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	margin: 0.5rem;
	margin-top: 0;
	padding: 1rem 0.5rem;
	border: 2px solid transparent;
	cursor: pointer;
	${(props) => props.selected && `border-color: ${props.theme.colors.apButton}`}
`;

const ImageText = styled.div<{selected?: boolean}>`
	display: flex;
	align-items: center;
	justify-content: center;
	height: 3rem;
	font-family: ${(props) => props.theme.fonts.family};
	${(props) => props.selected && `color: ${props.theme.colors.apButtonHover}`}
`;

const ModalSubtext = styled.div`
	margin-left: 1.25rem;
	margin-top: 1.25rem;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
`;

const ImgCarouselArrow = styled(FontAwesomeIcon)<{displayTypeValue: SnippetDisplayMode}>`
	position: absolute;
	font-size: 1.5rem;
	cursor: pointer;
	top: 21rem;
	${(props) => props.displayTypeValue === SnippetDisplayMode.SINGLE && "position: relative; top: 7.5rem;"}
`;

const ImgCarouselLeftArrow = styled(ImgCarouselArrow).attrs({ icon: faChevronLeft })<{
	displayTypeValue: SnippetDisplayMode;
}>`
	${(props) => props.displayTypeValue !== SnippetDisplayMode.SINGLE && "left: 2rem;"}
`;

const ImgCarouselRightArrow = styled(ImgCarouselArrow).attrs({ icon: faChevronRight })<{
	displayTypeValue: SnippetDisplayMode;
}>`
	${(props) => props.displayTypeValue !== SnippetDisplayMode.SINGLE && "right: 2rem;"}
`;

const CarouselItem = styled.div<{displayTypeValue: SnippetDisplayMode}>`
	margin: 10px;
	overflow: hidden;
	${(props) =>
		props.displayTypeValue !== SnippetDisplayMode.SINGLE &&
		`
			flex: 1 0 100%;
			flex-basis: calc(33.333% - 20px);
			transition: flex-basis 300ms ease-out;
		`}
	color: ${(props) => props.theme.colors.apTextColor};
`;

const BackButton = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-weight: bold;
	cursor: pointer;
`;

const VideoPerformance = styled.div`
	border-radius: 10px;
	font-family: ${(props) => props.theme.fonts.family};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	padding: 15px;
`;

const VideoPerformanceSection = styled(VideoPerformance)`
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
	justify-content: space-between;
	display: flex;
`;

const VideoMetricTitle = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
	font-size: 1rem;
	font-weight: 700;
`;

const ScorebarContainer = styled.div`
	width: 100%;
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	position: relative;
	border-radius: 5px;
	padding: 5px;
	display: flex;
`;

const SliderContainer = styled.div`
	width: 100%;
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	padding: 15px;
`;

const ScoreBar = styled.div<{score: number}>`
	width: ${(props) => props.score}%;
	background-color: ${(props) => {
		if (props.score < 21) return props.theme.colors.scoreBarLevel1;
		if (props.score < 41) return props.theme.colors.scoreBarLevel2;
		if (props.score < 61) return props.theme.colors.scoreBarLevel3;
		if (props.score < 81) return props.theme.colors.scoreBarLevel4;
		return props.theme.colors.scoreBarLevel5;
	}};
	height: 29px;
	transition: width 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 5px;
`;

const Score = styled.div<{displayOnSide?: boolean}>`
	position: absolute;
	text-align: center;
	line-height: 29px;
	color: ${(props) => props.theme.colors.apWhite};
	font-family: ${(props) => props.theme.fonts.family};

	${(props) => {
		if (props.displayOnSide) {
			return `
				position: relative;
				margin-left: 0.5rem;
				text-align: left;
				color: ${props.theme.colors.apBlack};
			`;
		}
	}};
`;

const VideoPercentageSubheading = styled.div`
	color: ${(props) => props.theme.colors.disabledTextColor};
	font-family: ${(props) => props.theme.fonts.family};
	text-align: center;
	margin-top: 0.5rem;
`;

const BarGraphContainer = styled.div<{largeGraph?: boolean}>`
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	height: ${(props) => (props.largeGraph ? "300px" : "100px")};
	padding: 10px;
	margin-top: 2.5rem;
	${(props) => props.largeGraph && "position: relative;"}

	${(props) =>
		props.largeGraph &&
		`@media (max-width: 930px) {
			height: 200px;
		}`};
`;

const BarGraphItemContainer = styled(FlexCol)`
	position: relative;
	height: 100%;
	justify-content: flex-end;
	margin: 0 0.25rem;
`;

const BarGraphItem = styled.div<{value: number; num?: number; zeroBottom?: boolean; metricType?: MetricTypes}>`
	width: 100%;
	padding-bottom: ${(props) => props.value}px;
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
	border-bottom: 5px solid ${(props) => props.theme.colors.apMedGrey};
	background-color: ${(props) => {
		if (props.metricType === MetricTypes.IMPRESSIONS) return props.theme.colors.apYellow;
		if (props.metricType === MetricTypes.PLAYS) return props.theme.colors.apBlue;
		if (props.metricType === MetricTypes.CLICKS) return props.theme.colors.apGreen;

		if (props.num === 1) return props.theme.colors.scoreBarLevel1;
		if (props.num === 2) return props.theme.colors.scoreBarLevel2;
		if (props.num === 3) return props.theme.colors.scoreBarLevel3;
		if (props.num === 4) return props.theme.colors.scoreBarLevel4;
		if (props.num === 5) return props.theme.colors.scoreBarLevel5;
	}};

	&::after {
		content: "";
		position: absolute;
		left: -5px;
		right: -5px;
		bottom: ${(props) => (props.zeroBottom ? "0" : "1.8rem")};
		border-bottom: 5px solid ${(props) => props.theme.colors.apMedGrey};
		${(props) => {
			if (props.num === 1) {
				return `border-top-left-radius: 3px;
				border-bottom-left-radius: 3px;`;
			}
			if (props.num === 5) {
				return `border-top-right-radius: 3px;
				border-bottom-right-radius: 3px;`;
			}
		}}
	}
`;

const BarGraphLabel = styled.div<{performance?: boolean}>`
	margin-top: 5px;
	text-align: center;
	font-family: ${(props) => props.theme.fonts.family};

	${(props) => {
		if (props.performance) {
			return `
			@media (max-width: 1530px) {
				font-size: 0.85rem;
			}
			`;
		}
	}}
`;

const BarGraphNumber = styled.div<{value: number}>`
	position: absolute;
	bottom: ${(props) => `calc(${props.value}px + 7.5px)`};
	width: 100%;
	text-align: center;
	z-index: 10;
`;

const GridLines = styled.div`
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	pointer-events: none;
	padding: 10px 10px 15px 10px;

	.line {
		width: 100%;
		border-top: 1px solid ${(props) => props.theme.colors.apMedGrey};

		&:last-child {
			visibility: hidden;
		}
	}
`;

const TeleprompterBox = styled.div`
	width: 100%;
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	padding: 15px;
`;


const TeleprompterTextArea = styled.textarea`
	background-color: ${(props) => props.theme.colors.apWhite};
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	color: ${(props) => props.theme.colors.apInputColor};
	width: 100%;
	border-radius: 10px;
	padding: 15px;
	resize: none;
	height: 300px;
	font-size: 15px;
	vertical-align: top;

	&:disabled {
		background-color: ${(props) => props.theme.colors.apWhite};
		color: ${(props) => props.theme.colors.disabledInput};
	}
`;

const PageSection = styled.div`
	border-radius: 10px;
	padding: 1.5rem;
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	font-family: ${(props) => props.theme.fonts.family};
`;

const PageSectionBlock = styled.div`
	border-radius: 10px;
	padding: 1.5rem;
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	font-family: ${(props) => props.theme.fonts.family};
	display: flex;
	flex-direction: column;
	height: 400px;
	align-items: center;
	justify-content: center;
	gap: 1.5rem;
`;

const BlurredDiv = styled.div`
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	box-shadow: 0 0 7px 7px ${(props) => props.theme.colors.apSectionBackground};
	margin: 20px 5px;
	height: 20px;
	width: 20px;
	border-radius: 3px;
`;

const BlurredLock = styled.div`
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	box-shadow: 0 0 7px 5px ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 3px;
	cursor: pointer;
	height: 20px;
	width: 20px;
	line-height: 0;
	fill: ${(props) => props.theme.colors.apGreyButton};
`;

const PageSubsection = styled(PageSection)`
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	width: 100%;
	margin: 1.5rem 0.5rem;
	padding: 1rem;

	&:first-child {
		margin-left: 0;
	}

	&:last-child {
		margin-right: 0;
	}

	@media (max-width: 930px) {
		margin-left: 0;
	}
`;

const MetricValue = styled.div`
	font-size: 2rem;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};

	@media (max-width: 1130px) {
		font-size: 1.5rem;
	}
`;

const MetricIncrease = styled.div`
	float: right;
	font-weight: bold;
	color: ${(props) => props.theme.colors.scoreBarLevel5};
`;

const MetricDecrease = styled.div`
	float: right;
	font-weight: bold;
	color: ${(props) => props.theme.colors.scoreBarLevel1};
`;

const LegendColour = styled.div<{metricType: MetricTypes}>`
	border-radius: 5px;
	width: 20px;
	height: 20px;
	margin: 0.5rem 1rem;
	background-color: ${(props) => {
		if (props.metricType === MetricTypes.IMPRESSIONS) return props.theme.colors.apYellow;
		if (props.metricType === MetricTypes.PLAYS) return props.theme.colors.apBlue;
		if (props.metricType === MetricTypes.CLICKS) return props.theme.colors.apGreen;
	}};
`;

const StyledDatePickerWrapper = styled.div`
	.react-datepicker {
		width: 346px !important;
		border: 1px solid ${(props) => props.theme.colors.snippetModalTextColor};
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
		color: ${(props) => props.theme.colors.apTextColor};
	}

	.react-datepicker__month-dropdown {
		width: 35%;
		left: auto;
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
		border: 1px solid ${(props) => props.theme.colors.snippetModalTextColor};
	}

	.react-datepicker__year-dropdown {
		width: 25%;
		left: auto;
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
		border: 1px solid ${(props) => props.theme.colors.snippetModalTextColor};
	}

	.react-datepicker__navigation--years-previous,
	.react-datepicker__navigation--years-upcoming {
		border-color: ${(props) => props.theme.colors.apBlack} transparent transparent transparent;
		width: 0;
		height: 0;
		border-style: solid;
	}

	.react-datepicker__day--outside-month {
		opacity: 0.5;
	}

	.react-datepicker__year-option {
		height: 20px;
	}

	.react-datepicker__year-option:hover {
		background-color: ${(props) => props.theme.colors.apSectionBackground};
	}

	.react-datepicker__month-option:hover {
		background-color: ${(props) => props.theme.colors.apSectionBackground};
	}

	.react-datepicker__navigation--years-previous {
		transform: scale(2) translateY(3px);
	}

	.react-datepicker__navigation--years-upcoming {
		border-bottom: transparent;
		transform: rotate(180deg) scale(2) translateY(-6px);
	}

	.react-datepicker__header {
		margin: 5px;
		border-radius: 6px;
		border-bottom: none;
		background-color: ${(props) => props.theme.colors.apSectionBackground};
	}

	.react-datepicker__day-names {
		margin-bottom: -56px;
		transform: translateY(-0.5rem);
		font-weight: bold;
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
	}

	.react-datepicker__day,
	.react-datepicker__day-name {
		width: 42px !important;
		color: ${(props) => props.theme.colors.apTextColor};
	}

	.react-datepicker__month {
		margin-top: 2.5rem;
	}

	.react-datepicker__month .react-datepicker__day--selected,
	.react-datepicker__month .react-datepicker__day--in-range {
		background-color: ${(props) => props.theme.colors.apButton} !important;
		color: white;
	}

	.react-datepicker__header__dropdown.react-datepicker__header__dropdown--scroll {
		margin-bottom: 24px;
	}

	.react-datepicker__day--keyboard-selected {
		background-color: unset;
		color: unset;
	}

	.react-datepicker__current-month {
		display: none;
	}

	.react-datepicker__day--in-selecting-range: not(
		.react-datepicker__day--in-range,
		.react-datepicker__month-text--in-range,
		.react-datepicker__quarter-text--in-range,
		.react-datepicker__year-text--in-range
	) {
		background-color: ${(props) => props.theme.colors.apButton}80;
	}

	.react-datepicker__day:hover {
		background-color: ${(props) => props.theme.colors.apButton}80;
	}

	.react-datepicker__month-read-view--selected-month,
	.react-datepicker__year-read-view--selected-year {
		padding: 0.35rem 3.5rem 0.35rem 0.6rem;
		border-radius: 5px;
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
	}

	.react-datepicker__month-read-view--down-arrow,
	.react-datepicker__year-read-view--down-arrow {
		right: 12px;
		top: 3px;
		border-color: ${(props) => props.theme.colors.apTextColor};
	}

	.react-datepicker__navigation--next,
	.react-datepicker__navigation--previous {
		top: 15px;

		.react-datepicker__navigation-icon--next::before,
		.react-datepicker__navigation-icon--previous::before {
			border-color: ${(props) => props.theme.colors.apTextColor};
		}
	}

	position: absolute;
	top: calc(100% + 0.5rem);
	right: -5px;
	z-index: 10;

	@media (max-width: 768px) {
		right: -50px;
	}
`;

const DateRange = styled.div`
	display: flex;
	width: 21rem;
	padding: 1rem;
	align-items: center;
	justify-content: space-around;
	border-radius: 33px;
	right: 0;
	cursor: pointer;
	font-family: ${(props) => props.theme.fonts.fontFamily};
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};

	@media (max-width: 930px) {
		width: 15rem;
	}
`;

const ScoreCircle = styled.div<{score: number}>`
	border-radius: 21px;
	padding: 0.5rem;
	text-align: center;
	color: ${(props) => props.theme.colors.apWhite};
	background-color: ${(props) => {
		if (props.score < 21) return props.theme.colors.scoreBarLevel1;
		if (props.score < 41) return props.theme.colors.scoreBarLevel2;
		if (props.score < 61) return props.theme.colors.scoreBarLevel3;
		if (props.score < 81) return props.theme.colors.scoreBarLevel4;
		return props.theme.colors.scoreBarLevel5;
	}};
`;

const PerformanceBarGraphContainer = styled(Flex)`
	@media (max-width: 930px) {
		flex-direction: column;
	}
`;

const MetricDisplayContainer = styled(Flex)`
	@media (max-width: 930px) {
		flex-direction: column;
	}
`;

const ArrowIcon = styled(FontAwesomeIcon).attrs({ icon: faArrowRightLong })`
	color: ${(props) => props.theme.colors.apTextColor};
`;

const RefreshIcon = styled(FontAwesomeIcon).attrs({ icon: faArrowRotateRight })`
	color: ${(props) => props.theme.colors.apWhite};
`;

const RefreshButton = styled(MainButton)`
	@media (max-width: 930px) {
		padding: 1rem 1.3rem;
	}
`;

const PerformanceHeader = styled(PageRow)`
	@media (max-width: 930px) {
		flex-direction: column;
	}
`;

const TopVideos = styled(Flex)`
	justify-content: space-between;
	color: ${(props) => props.theme.colors.apTextColor};
	align-items: center;
	@media (max-width: 930px) {
		flex-direction: column;
	}
`;

const ScoreBarDiv = styled.div<{scoreBarLevel: number; disabled?: boolean}>`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apWhite};
	font-size: 1rem;
	border-radius: 21px;
	padding: 0.5rem 1rem;
	text-align: center;
	display: flex;
	align-items: center;
	height: fit-content;
	margin-right: -1rem;

	${(props) =>
		props.disabled &&
		css`
			color: ${(props) => props.theme.colors.disabledTextColor};
		`}

	background-color: ${(props) => {
		if (props.scoreBarLevel === 1) return props.theme.colors.scoreBarLevel1;
		if (props.scoreBarLevel === 2) return props.theme.colors.scoreBarLevel2;
		if (props.scoreBarLevel === 3) return props.theme.colors.scoreBarLevel3;
		if (props.scoreBarLevel === 4) return props.theme.colors.scoreBarLevel4;
		if (props.scoreBarLevel === 5) return props.theme.colors.scoreBarLevel5;
	}};
`;

const StatsIcon = styled.div<{mobile?: boolean}>`
	height: 13px;
	width: 15px;
	margin: 20px 0px;
	line-height: 0;
	fill: ${(props) => props.theme.colors.apTextColor};

	${(props) =>
		props.mobile &&
		css`
			margin: 5px 10px 5px 0px;
		`}
`;

const InfoBox = styled.div`
	border-radius: 10px;
	position: absolute;
	z-index: 10;
	padding: 1rem;
	width: 17rem;
	top: -3rem;
	left: 2rem;
	color: ${(props) => props.theme.colors.apCopyIconHover};
	border: 1px solid ${(props) => props.theme.colors.apMedGrey};
	background-color: ${(props) => props.theme.colors.apWhite};

	@media (max-width: 1050px) {
		left: -7.75rem;
		top: -5rem;
	}
`;

const SelectorRow = styled.div`
	display: flex;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
	scrollbar-width: none;
`;

const LetterButton = styled.button<{isSelected: boolean}>`
	padding: 5px 10px;
	margin: 5px;
	border: none;
	background-color: transparent;
	cursor: pointer;
	white-space: nowrap;
	font-weight: bold;
	color: ${(props) => props.theme.colors.apLowMedGrey};
	${(props) => props.isSelected && `color: ${props.theme.colors.apButton}`};
`;

const BulkUploadContainer = styled.div`
	width: 100%;
	padding: 0.5rem;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20px;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apFormText};

	&:hover {
		filter: brightness(0.95);
	}
`;

const VideoGrid = styled.div<{selectionMode?: boolean}>`
	display: grid;
	grid-template-columns: repeat(auto-fill, 225px);
	gap: 2rem;
	margin-top: 2rem;
	justify-content: center;

	${(props) =>
		props.selectionMode &&
		`
		grid-template-columns: repeat(auto-fill, 160px);
		gap: 1rem;
		margin-top: 0;
		`};
`;

const VideoTile = styled.div<{selectionMode?: boolean}>`
	background-color: ${(props) => props.theme.colors.apBlack};
	border-radius: 8px;
	overflow: hidden;
	height: 225px;
	width: 225px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apWhite};

	&:hover .icon-hover {
		display: block;
	}

	${(props) =>
		props.selectionMode &&
		`
		height: 160px;
		width: 160px;
		`};
`;

const VideoPoster = styled.img<{selectionMode?: boolean}>`
	width: 100%;
	height: 225px;
	object-fit: contain;
	cursor: pointer;
	transition: filter 0.2s ease;

	&:hover {
		filter: brightness(0.5);
	}

	${(props) =>
		props.selectionMode &&
		`
		height: 160px;
		`};
`;

const VideoInfo = styled.div<{selectionMode?: boolean}>`
	font-size: 1rem;
	margin-top: 0.75rem;
	width: 90%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apFormText};
`;

const VideoUploadTime = styled.div<{selectionMode?: boolean}>`
	margin-top: 0.25rem;
	font-size: 0.7rem;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apThirdButtonActive};
`;

const TrashIcon = styled(FontAwesomeIcon).attrs({ icon: faTrash })`
	color: ${(props) => props.theme.colors.apTextColor};
	position: absolute;
	top: 0;
	right: 0;
	margin-top: 0.75rem;
	margin-right: 0.75rem;
	cursor: pointer;
	display: none;
`;

const WarningIcon = styled.img`
	width: 40px;
	display: block;
	margin: auto;
	margin-bottom: 0.5rem;
`;

const DropdownWrapper = styled.div`
	position: absolute;
	right: 5rem;
	top: 3rem;
	font-weight: bold;
	color: ${(props) => props.theme.colors.apFormText};
`;

const DropdownButton = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	width: fit-content;
	border-radius: 50px;
	border: none;
	background-color: ${(props) => props.theme.colors.apInputBackground};
	padding: 10px 12px;
	cursor: pointer;
`;

const DropdownContent = styled.div<{open?: boolean}>`
	display: ${(props) => (props.open ? "block" : "none")};
	position: absolute;
	background-color: ${(props) => props.theme.colors.apInputBackground};
	min-width: 160px;
	z-index: 9999;
	border-radius: 15px;
	padding: 5px 0;
	border: 1px solid ${(props) => props.theme.colors.apLowMedGrey};
	top: 3.25rem;
	right: 0;
	overflow: hidden;
`;

const DropdownItem = styled.div`
	padding: 12px 16px;
	text-decoration: none;
	display: block;
	cursor: pointer;

	&:hover {
		background-color: ${(props) => props.theme.colors.apLowMedGrey};
	}
`;

const IGConnectContainer = styled(Flex)`
	padding: 1rem;
	background-color: ${(props) => props.theme.colors.apInputBackground};
	justify-content: space-between;
	width: 28rem;
	border-radius: 15px;
	margin: auto;
`;

const TabRow = styled.div`
	display: flex;
	gap: 2rem;
	margin: 2rem 0;
	font-size: 1.2rem;
	font-weight: bold;

	@media (max-width: 500px) {
		gap: 1rem;
		justify-content: space-between;
	}

	@media (max-width: 430px) {
		font-size: 1rem;
	}
`;

const TabHeading = styled.div<{selected: boolean}>`
	cursor: pointer;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => (props.selected ? props.theme.colors.apTextColor : props.theme.colors.apThirdButtonActive)};

	${(props) =>
		props.selected &&
		`
		&::after {
			content: "";
			height: 3px;
			width: 40px;
			display: block;
			margin: auto;
			margin-top: 0.5rem;
			border-radius: 25px;
			background-color: ${props.theme.colors.apButtonActive};
		}
	`}
`;

const RegularText = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apTextColor};
`;

const WhiteText = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => props.theme.colors.apWhite};
`

const SortBox = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	padding: 5px 10px;
	border-radius: 5px;
	width: max-content;
	cursor: pointer;
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apInputColor};
	display: flex;
	flex-direction: row;
	align-items: center;

	span {
		margin-right: 5px;
	}

	img {
		max-width: 12px;
		max-height: 12px;
	}

	@media (max-width: 768px) {
		font-size: 12px;
		margin: auto;
		padding: 5px;
	}
`;

const SectionText = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	font-weight: bold;
`;

const SelectionContainer = styled(Flex)`
	border-radius: 10px;
	padding: 5px;
	align-items: center;
	border: 2px solid ${(props) => props.theme.colors.apBlack};
	font-family: ${(props) => props.theme.fonts.family};
	position: relative;
`;

const SelectionButton = styled.div<{selected: boolean}>`
	padding: 14px 17px;
	border-radius: 7px;
	cursor: pointer;
	width: 6rem;
	text-align: center;
	z-index: 2;
	transition: color 0.3s ease;

	${(props) =>
		props.selected &&
		`
			color: ${props.theme.colors.apWhite};
	`}
`;

const SelectionAddOn = styled.div<{selected?: boolean}>`
	padding: 13px;
	margin: 0.5rem;
	border-radius: 7px;
	background: ${(props) => props.theme.colors.apInputBackground};
	font-family: ${(props) => props.theme.fonts.family};
	transition: background 0.5s ease;
	transition: color 0.5s ease;

	${(props) =>
		props.selected &&
		`
			background: ${props.theme.colors.apBlack};
			color: ${props.theme.colors.apWhite};
	`}
`;

const H2 = styled.h2`
	font-family: ${(props) => props.theme.fonts.family};
	font-weight: bold;
`;

const PlanOption = styled.div<{upgrade?: boolean}>`
	font-family: ${(props) => props.theme.fonts.family};
	padding: 1.5rem;
	border-radius: 25px;
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	height: 100%;
	${(props) =>
		props.upgrade &&
		`
			background: ${props.theme.colors.apInputBackground};
	`}
`;

const SelectionInfo = styled(SelectionAddOn)`
	color: ${(props) => props.theme.colors.proLabelBorder};
	background: ${(props) => props.theme.colors.proLabelBackground};
	border: 1px solid ${(props) => props.theme.colors.proLabelBorder};

	padding: 4px 13px;
	border-radius: 5px;
	margin-top: 4px;
	height: fit-content;

	${(props) =>
		props.selected &&
		`
			color: ${props.theme.colors.labelBorder};
			background: ${props.theme.colors.labelBackground};
			border: 1px solid ${props.theme.colors.labelBorder};
	`}
`;

const OriginalPrice = styled(H2)<{strikethrough?: boolean; lightText?: boolean}>`
	font-size: 1.5rem;
	font-weight: normal;
	color: ${(props) => props.theme.colors.apBlack};

	${(props) =>
		props.lightText &&
		`
			color: ${props.theme.colors.apThirdButtonActive};
		`}

	${(props) =>
		props.strikethrough &&
		`
		color: ${props.theme.colors.apThirdButtonActive};
		text-decoration: line-through;
	`}
`;

const DiscountedPrice = styled(OriginalPrice)<{displayPrice: boolean}>`
	color: ${(props) => props.theme.colors.apBlack};
	opacity: 0;
	position: absolute;
	top: 0;
	transition: top 0.1s, opacity 0.1s;

	${(props) =>
		props.displayPrice &&
		`
		top: 2rem;
		opacity: 1;
	`}
`;

const ModalSectionFullHeight = styled.div<{backgroundOpt: number; position: "right" | "left" | "center"}>`
	height: 100%;
	max-width: 35rem;
	padding: 3rem;
	width: 100%;

	${(props) =>
		props.backgroundOpt === 0
			? `
			background-color: ${props.theme.colors.apBackgroundColor};
			color: ${props.theme.colors.apTextColor};
		`
			: props.backgroundOpt === 1
				? `
			background-color: ${props.theme.colors.apButton};
			color: ${props.theme.colors.apButtonColor};
		`
				: props.backgroundOpt === 2
					? `
			background-color: ${props.theme.colors.apSectionBackground};
			color: ${props.theme.colors.apTextColor};
		`
					: `
			background-color: #ff41b5;
			color: ${props.theme.colors.apWhite};
		`}

	${(props) =>
		props.position === "left"
			? `
			border-top-left-radius: 1.25rem;
			border-bottom-left-radius: 1.25rem;
			border-top-right-radius: 0;
			border-bottom-right-radius: 0;
		`
			: props.position === "right"
				? `
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
			border-top-right-radius: 1.25rem;
			border-bottom-right-radius: 1.25rem;
		`
				: `
			border-top-left-radius: 1.25rem;
			border-bottom-left-radius: 1.25rem;
			border-top-right-radius: 1.25rem;
			border-bottom-right-radius: 1.25rem;
		`}

	@media (max-width: 768px) {
		padding: 1.2rem;
		font-size: 14px;

		${(props) =>
			props.position === "left"
				? `
				border-top-left-radius: 1.25rem;
				border-top-right-radius: 1.25rem;
				border-bottom-left-radius: 0;
				border-bottom-right-radius: 0;
			`
				: props.position === "right"
					? `
				border-top-left-radius: 0;
				border-top-right-radius: 0;
				border-bottom-left-radius: 1.25rem;
				border-bottom-right-radius: 1.25rem;
			`
					: `
				border-top-left-radius: 1.25rem;
				border-top-right-radius: 1.25rem;
				border-bottom-left-radius: 1.25rem;
				border-bottom-right-radius: 1.25rem;
			`}
	}
`;

const Bold = styled.div`
	font-weight: bold;
`;

const BlueBox = styled.div`
	border: 2px solid ${(props) => props.theme.colors.logoBackground};
	border-radius: 10px;
	padding: 15px 20px;

	@media (max-width: 768px) {
		padding: 15px 10px;
	}
`;

const ProFeatures = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	padding: 15px 20px;
	margin: 10px 0;
	font-size: 18px;

	@media (max-width: 768px) {
		font-size: 13px;
	}
`;

const LinkDiv = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	padding: 5px;
	cursor: pointer;
	font-size: 20px;
	text-align: center;

	&:hover {
		color: ${(props) => props.theme.colors.apButtonHover};
	}

	@media (max-width: 768px) {
		font-size: 14px;
	}
`;

const TrialTag = styled.div`
	background: ${(props) => props.theme.colors.trialBackground};
	color: ${(props) => props.theme.colors.trialColor};
	border: 1px solid ${(props) => props.theme.colors.trialColor};
	padding: 0px 5px;
	border-radius: 5px;
	min-width: 100px;
	height: fit-content;
	margin-bottom: 10px;

	@media (max-width: 768px) {
		font-size: 14px;
		margin-bottom: 5px;
	}
`;

const LinkText = styled.div`
	font-weight: bold;
	margin-left: 15px;
	font-size: 20px;

	@media (max-width: 768px) {
		font-size: 14px;
	}
`;

const ApProFeatures = styled.div`
	font-size: 0.9rem;
	margin-left: 27px;
	margin-top: 0.5rem;
	margin-bottom: 0.5rem;
`;

const StarIcon = styled(FontAwesomeIcon).attrs({ icon: faStar })`
	color: #ffc526;
`;

const PlanContainer = styled(Flex)`
	margin-top: 1rem;
	overflow: auto;

	@media (max-width: 768px) {
		flex-direction: column;
	}
`;

const BillingFrequencyContainer = styled(Flex)`
	justify-content: center;
	align-items: center;
	font-weight: bold;
	@media (max-width: 768px) {
		flex-direction: column;
	}
`;

const ModalSectionContainer = styled(Flex)`
	@media (max-width: 768px) {
		flex-direction: column;
	}
`;

const BillingFrequencySlider = styled.div<{position: number}>`
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	background-color: black;
	z-index: 1;
	transition: transform 0.3s ease;
	margin: 4px 3px;
	border-radius: 7px;
	width: ${(props) => (props.position === 0 ? "50%" : "48.5%")};
	transform: ${(props) => `translateX(${props.position * 100}%)`};
`;

const StripeFieldContainer = styled.div`
	border: 1px solid #ccc;
	border-radius: 5px;
	padding: 1rem;
`;

const PlansPageDescription = styled.div`
	@media (min-width: 1300px) and (max-width: 1980px) {
		min-height: 50px;
	}
	@media (min-width: 768px) and (max-width: 1300px) {
		min-height: 80px;
	}
`;

const PlansPageUl = styled.ul`
	padding-bottom: 10px;
`;

const SelectPlanContainer = styled.div`
	background: ${(props) => props.theme.colors.apWhite};
	border: 1px solid ${(props) => props.theme.colors.apInputBackground};
	border-radius: 5px;
	padding: 1rem;

	select {
		-moz-appearance: none;
		-webkit-appearance: none;
		appearance: none;
		width: 100%;
		background-image: url("data:image/svg+xml,${encodeURIComponent(
			"<svg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='sort' class='svg-inline--fa fa-sort' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'><path fill='#DEDDDD' d='M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8H32c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8H288c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z'></path></svg>"
		)}") !important;
		background-size: 16px 16px;
		background-repeat: no-repeat;
		background-position: right;
		border: 1px solid ${(props) => props.theme.colors.apWhite};
		background-color: ${(props) => props.theme.colors.apWhite};
		color: ${(props) => props.theme.colors.apInputColor};

		&:focus {
			outline: none;
			box-shadow: none;
		}
	}
`;

const VideoWrapper = styled.div`
	background: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 10px;
	overflow: hidden;
	height: 500px;
`;

const FlexSwitchRow = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	color: ${(props) => props.theme.colors.apInputColor};
	border-radius: 10px;
	padding: 0.5rem;
	max-width: 310px;
`;

const FlexSwitchCol = styled.div<{active?: boolean}>`
	color: ${(props) => props.theme.colors.apInputColor};
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: bold;
	border-radius: 5px;
	padding: 0.3rem 1rem;
	cursor: pointer;
	opacity: 0.5;
	font-family: "Readex Pro", sans-serif;

	${(props) =>
		props.active &&
		css`
			opacity: 1;
			border: 1px solid ${(props) => props.theme.colors.apBackgroundColor};
			background-color: ${(props) => props.theme.colors.apBackgroundColor};
		`}
`;

const PortraitBox = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apTextColor};
	background-color: ${(props) => props.theme.colors.apTextColor};
	border-radius: 2px;
	height: 24px;
	width: 15px;
	margin-right: 10px;
`;

const LandscapeBox = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apTextColor};
	background-color: ${(props) => props.theme.colors.apTextColor};
	border-radius: 2px;
	height: 17px;
	width: 25px;
	margin-right: 10px;
`;

const ColorBoxContainer = styled.div`
	position: relative;
	display: flex;
	width: 130px;
	height: 40px;
	border-radius: 5px;
	padding: 0.6rem 1rem;
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
	border: 1px solid ${(props) => props.theme.colors.apBackgroundColor};
	outline: none;
	appearance: none;
`;

const ColorBox = styled.div<{color?: string}>`
	border-radius: 5px;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 0.5rem;
	text-align: center;
	background: ${(props) => props.theme.colors.apWhite};
	border: 1px solid ${(props) => props.theme.colors.snippetBorder};
	cursor: pointer;
	${(props) =>
		props.color &&
		css`
			background: ${() => props.color};
		`}
`;

const ColorInput = styled.input`
	display: block;
	outline: none;
	appearance: none;
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
	border: none;
	width: 100px;
	padding-left: 0.5rem;
	text-transform: uppercase;
	cursor: pointer;
	&:hover,
	&:focus {
		outline: none;
	}
	&::placeholder {
		font-size: 0.6rem;
	}
`;

const EyeDropperIcon = styled(FontAwesomeIcon).attrs({ icon: faEyeDropper })`
	color: ${(props) => props.theme.colors.apTextColor};
`;

const FlexRow = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: ${(props) => props.theme.colors.apTextColor};
`;

const ListTabRow = styled.div`
	border: 1px solid ${(props) => props.theme.colors.apSectionBackground};
	background-color: ${(props) => props.theme.colors.apSectionBackground};
	border-radius: 20px;
	padding: 18px;
	font-size: 0.9rem;
`;

const ListTabHeading = styled.div<{pro: boolean}>`
	cursor: pointer;
	font-family: ${(props) => props.theme.fonts.family};
	color: ${(props) => (props.pro ? props.theme.colors.apTextColor : props.theme.colors.apThirdButtonActive)};
	display: flex;
	justify-content: space-between;
	font-size: 1rem;
`;

const ListTabArrow = styled.div`
	line-height: 0;
	width: 15px;
`;

const BasicAccountSection = styled.div`
	opacity: 0.5;
	pointer-events: none;
`;


const Container = styled.div`
	max-width: 1200px;
	margin: 0 auto;
	padding: 2rem;
`;

const Section = styled.div`
	margin-bottom: 4rem;
`;

const SectionHeader = styled.div`
	margin-bottom: 2rem;
`;

const VideoPreview = styled.div<{ background?: boolean, imageUrl?: string }>`
		/* display: flex;
		flex-direction: column;
		justify-content: center; */
		display: table;
		align-items: center;
		width: 100%;
		border-radius: 10px;
		height: 500px;

		${(props) =>
			props.background &&
			`
			background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${props.imageUrl});
			background-repeat: no-repeat;
			background-position: center;
			background-size: cover;
		`}
`;

const TableCell = styled.div`
	display: table-cell;
	vertical-align: middle;
	color: ${(props) => props.theme.colors.apWhite};
`;

const SectionTitle = styled.h2`
	font-size: 1.8rem;
	font-weight: 600;
	color: ${(props) => props.theme.colors.apTextColor};
	margin: 0;
`;

const SectionDescription = styled.p<{ blackColor?: boolean }>`
	font-size: 1rem;
	color: ${(props) => props.theme.colors.apSecondaryTextColor};
	margin: 0.5rem 0 0 0;
		${(props) =>
			props.blackColor &&
		`
			color: ${props.theme.colors.apTextColor};
		`}
`;

const AvatarGrid = styled.div`
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(255px, 1fr));
	gap: 2rem;
`;

const AvatarCard = styled.div<{ selected?: boolean }>`
	background: ${(props) => props.theme.colors.apBackgroundColor};
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	cursor: pointer;
	border: 3px solid ${(props) => props.selected ? props.theme.colors.apButton : "transparent"};
	position: relative;
	height: 250px;
		width: 250px;
		margin: auto;
	text-align: center;

	&:hover {
		box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
	}

	${(props) => props.selected && `
		&::before {
						content: "";
			background-image: url(${GreenCheckIcon});
						background-size: cover;
						background-position: center;
			position: absolute;
			top: 1rem;
			right: 1rem;
			width: 24px;
			height: 24px;
			z-index: 2;
		}
	`}
`;

const AvatarImage = styled.img`
	width: 100%;
	height: 100%;
	object-fit: cover;
`;

const CustomAvatarSection = styled.div<{border?: boolean, scroll?: boolean, center?: boolean}>`
	background: ${(props) => props.theme.colors.apSectionBackground};
	color: ${(props) => props.theme.colors.apTextColor};
	height: 100%;
	border-radius: 10px;
	padding: 3rem;
	text-align: center;

		${(props) =>
			props.center &&
		`
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		`}

		${(props) =>
			props.border &&
		`
			border: 3px solid ${props.theme.colors.apButton};
		`}

		${(props) =>
			props.scroll &&
		`
			padding: 0.5rem;
			overflow-y: scroll;
			overflow-x: hidden;
			-ms-overflow-style: none;
			scrollbar-width: none;
			max-height: 600px;
			&::-webkit-scrollbar {
				display: none;
			}
		`}
`;

const UploadTitle = styled.h3`
	font-size: 1.5rem;
	font-weight: 600;
	color: ${(props) => props.theme.colors.apTextColor};
	margin-bottom: 1rem;
`;

const UploadDescription = styled.p`
	font-size: 1rem;
	color: ${(props) => props.theme.colors.apSecondaryTextColor};
	line-height: 1.6;
`;

const FormGroup = styled.div`
	margin-bottom: 2rem;
`;

const Label = styled.label`
	display: block;
	font-size: 1.2rem;
	font-weight: 600;
	color: ${(props) => props.theme.colors.apTextColor};
	margin-bottom: 0.75rem;
`;

const VoiceLabel = styled.label`
	font-size: 1.3rem;
	font-weight: 600;
	color: ${(props) => props.theme.colors.apTextColor};
`;

const VoiceLabelBox = styled.label`
	font-size: 0.8rem;
	font-weight: 600;
	color: ${(props) => props.theme.colors.apTextColor};
		background-color: ${(props) => props.theme.colors.apSectionBackground};
	padding: 5px 10px;
	border-radius: 10px;
		width: fit-content;
`;

const StepsSection = styled.div`
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-bottom: 2rem;
		max-width: 700px;
		flex-wrap: wrap;

		@media (max-width: 500px) {
			justify-content: center;
			gap: 0.5rem;
		}
`;

const StepCircle = styled.div<{ active?: boolean }>`
		width: 24px;
		height: 24px;
		border-radius: 50%;
		background: ${(props) =>
			props.active ? props.theme.colors.stepActive : props.theme.colors.stepInactive};
		color: white;
		font-size: 0.9rem;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
`;

const StepText = styled.div<{ active?: boolean }>`
		color: ${(props) =>
			props.active ? props.theme.colors.stepActive : props.theme.colors.stepInactive};
		font-size: 0.95rem;
		margin-right: 0.75rem;
		font-weight: 500;
		white-space: nowrap;
`;

const StepLine = styled.div<{ active?: boolean }>`
		flex: 1;
		height: 1px;
		background: ${(props) =>
			props.active ? props.theme.colors.stepActive : props.theme.colors.stepInactive};
		min-width: 30px;

		@media (max-width: 550px) {
			display: none;
		}
`;


const Select = styled.select`
	width: 100%;
	padding: 1rem;
	border: 2px solid ${(props) => props.theme.colors.apBackgroundColor};
	border-radius: 12px;
	font-size: 1rem;
	background: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
	transition: all 0.2s ease;

	&:focus {
		outline: none;
		border-color: ${(props) => props.theme.colors.apButton};
		box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
	}
`;

const SmallSelect = styled.select`
	width: 100%;
	height: 34px;
	padding: 0.25rem;
	border: 2px solid ${(props) => props.theme.colors.apBackgroundColor};
	border-radius: 6px;
	font-size: 1rem;
	background: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
	transition: all 0.2s ease;

	&:focus {
		outline: none;
		border-color: ${(props) => props.theme.colors.apButton};
		box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
	}
`;

const SmallSelectDiv = styled.div`
	width: 100%;
	height: 34px;
	padding: 0.25rem;
	border: 2px solid ${(props) => props.theme.colors.apBackgroundColor};
	border-radius: 6px;
	font-size: 1rem;
	background: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apTextColor};
`;

const SmallSelectButton = styled.div`
	width: 100%;
	height: 34px;
	padding: 0.25rem;
	border-radius: 6px;
	font-size: 1rem;
	border: 2px solid ${(props) => props.theme.colors.apButton};
	background: ${(props) => props.theme.colors.apButton};
	color: ${(props) => props.theme.colors.apButtonColor};
	font-family: ${(props) => props.theme.fonts.family};
	text-align: center;
	cursor: pointer;

	&:hover {
		border-color: ${(props) => props.theme.colors.apButtonHover};
		background-color: ${(props) => props.theme.colors.apButtonHover};
	}
	&:active {
		border-color: ${(props) => props.theme.colors.apButtonActive};
		background-color: ${(props) => props.theme.colors.apButtonActive};
		box-shadow: inset 0px 3px 6px ${(props) => props.theme.colors.apButtonShadow};
	}
	&:disabled {
		border-color: ${(props) => props.theme.colors.apButtonActive};
		background-color: ${(props) => props.theme.colors.apButtonActive};
		box-shadow: inset 0px 3px 6px ${(props) => props.theme.colors.apButtonShadow};
		opacity: 0.5;
	}
`;

const TextArea = styled.textarea`
	border: 1px solid ${(props) => props.theme.colors.apInputBackground};
	background-color: ${(props) => props.theme.colors.apInputBackground};
	color: ${(props) => props.theme.colors.apInputColor};
	width: 100%;
	border-radius: 10px;
	padding: 1rem;
	resize: none;
	height: 280px;
	font-size: 15px;

	&:disabled {
		background-color: ${(props) => props.theme.colors.apInputBackground};
		color: ${(props) => props.theme.colors.disabledInput};
	}
`;

const ActionButton = styled.button<{ variant?: string }>`
	padding: 1rem 2rem;
	border: none;
	border-radius: 10px;
	font-size: 1rem;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
	min-width: 140px;
	background: ${(props) => props.theme.colors.apButton};
	color: ${(props) => props.theme.colors.apButtonColor};
	fill: ${(props) => props.theme.colors.apButtonColor};

	&:hover {
		opacity: 0.7;
	}

	${(props) => props.variant == "red" && `
		background: ${props.theme.colors.apButtonRed};
		color: ${props.theme.colors.apWhite};
		fill: ${props.theme.colors.apWhite};
	`}

	&:disabled {
		background: ${(props) => props.theme.colors.apGreyButton};
		color: ${(props) => props.theme.colors.apWhite};
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
				opacity: 1;
	}
`;

const LoadingOverlay = styled.div`
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 1000;
`;

const LoadingContent = styled.div`
	background: ${(props) => props.theme.colors.apWhite};
	border-radius: 20px;
	padding: 3rem;
	text-align: center;
	max-width: 400px;
	width: 90%;
	margin: auto;
`;

const LoadingTitle = styled.h3`
	font-size: 1.5rem;
	font-weight: 600;
	color: ${(props) => props.theme.colors.apTextColor};
	margin: 1rem 0 0.5rem;
`;

const LoadingDescription = styled.p`
	font-size: 1rem;
	color: ${(props) => props.theme.colors.apOffBlack};
	margin: 0;
	line-height: 1.5;
		max-width: 400px;
	text-align: center;
`;

const ButtonGroup = styled.div`
	display: flex;
	gap: 1rem;
	justify-content: center;
	margin-top: 2rem;
`;

const PreviewImage = styled.img`
	width: 160px;
	height: 160px;
	object-fit: cover;
	border-radius: 12px;
	margin: 0px auto 1rem auto;
	display: block;
	border: 2px solid ${(props) => props.theme.colors.apGreyButton};
`;


export {
	SidebarContainer,
	StyledNavItem,
	SidebarNotch,
	SidebarCircleContainer,
	MetricIcon,
	InlineMetricIcon,
	InlineIcon,
	NavIcon,
	BtnIcon,
	FormText,
	CustomSelect,
	StatsIcon,
	ScoreBarDiv,
	ChevronRight,
	HeaderDiv,
	LeftSide,
	RightSide,
	FlexEnd,
	PageBody,
	PageRow,
	CompanySection,
	FlexCol,
	FullHeightRow,
	EmptySection,
	OneCollection,
	VideosSection,
	CheckboxList,
	FlexSpace,
	FlexColor,
	CustomSwitch,
	TooltipWrapper,
	LeftImage,
	ImageBox,
	DivWithImage,
	ImgContentPanel,
	ImgContentPanelText,
	ImgContentPanelTitle,
	ImgContentPanelCopy,
	ImageButton,
	MainButton,
	MainSuccessButton,
	ThirdButton,
	GreyButton,
	ProLinkButton,
	ProIconHeader,
	LinkButton,
	CancelPlan,
	ImpressionsNumbers,
	BlueTextLink,
	WhiteTextLink,
	TextLink,
	PlanItemBtn,
	LinkSpan,
	AlertLinkSpan,
	Flex,
	FlexCheckbox,
	ListBox,
	ListGridBox,
	HeaderListTitle,
	ListItemContainer,
	FileDropBox,
	NoFileBox,
	TableBox,
	ListRowItem,
	ListRowFlex,
	ListRowGridItem,
	ListRowGridItemBox,
	UserListStatus,
	LogoBox,
	LogoImageBox,
	CompanyLogo,
	InitialsBox,
	CompanyImage,
	CompanyImageHeader,
	ApLogo,
	ApNavLogo,
	Ap404Logo,
	LinkSectionModal,
	VideoPageTitleText,
	HomePageTitle,
	HighlightCodeModal,
	TitleText,
	CodeSnippetContainer,
	CodeSnippetModal,
	CodeSnippet,
	SnippetModalText,
	VideoSectionBox,
	VideoPlayer,
	ButtonIconImg,
	ButtonIconDiv,
	PreviewGif,
	TabTitleImg,
	TabTitle,
	CanvasImage,
	ProgressBarCustom,
	ThemedProgressBar,
	ProgressBarTime,
	UploadImagePreview,
	CoverSectionModal,
	CaptionsSectionModal,
	CaptionsRightColumn,
	CaptionsBox,
	EditCaptionsArea,
	VideoCaptionsText,
	VideoPlayerText,
	CustomHighlight,
	NoImageFileBox,
	UploadImage,
	CoverImageBox,
	ProductsBox,
	LearnMoreDiv,
	VideoManager,
	ProductImage,
	SpanColor,
	SpanGrey,
	VideoSectionModal,
	CustomInputBox,
	UploadProductImage,
	HighlightCode,
	ImageCollections,
	ImageCarousel,
	WhiteBoxImage,
	WhiteBox,
	HttpErrorContainer,
	HttpErrorTitle,
	HttpErrorCode,
	HttpErrorMessage,
	InvitePending,
	InviteActions,
	FetchingProductText,
	SelectItem,
	SelectItemTitle,
	SelectItemContainer,
	SelectItemSubtext,
	RemoveIcon,
	MockImage,
	MockImageLarge,
	ImageSelectContainer,
	ImageText,
	ModalSubtext,
	ImgCarouselLeftArrow,
	ImgCarouselRightArrow,
	CarouselItem,
	BackButton,
	UploadBox,
	VideoPerformance,
	VideoPerformanceSection,
	VideoMetricTitle,
	VideoPercentageSubheading,
	ScorebarContainer,
	SliderContainer,
	ScoreBar,
	Score,
	BarGraphContainer,
	BarGraphItemContainer,
	BarGraphItem,
	BarGraphLabel,
	BarGraphNumber,
	GridLines,
	PageSectionBlock,
	BlurredDiv,
	BlurredLock,
	PageSection,
	PageSubsection,
	MetricValue,
	MetricIncrease,
	MetricDecrease,
	LegendColour,
	StyledDatePickerWrapper,
	DateRange,
	ScoreCircle,
	PerformanceBarGraphContainer,
	MetricDisplayContainer,
	ArrowIcon,
	RefreshIcon,
	RefreshButton,
	PerformanceHeader,
	TopVideos,
	InfoBox,
	SelectorRow,
	LetterButton,
	BulkUploadContainer,
	VideoGrid,
	VideoTile,
	VideoPoster,
	VideoInfo,
	VideoUploadTime,
	TrashIcon,
	WarningIcon,
	DropdownWrapper,
	DropdownButton,
	DropdownContent,
	DropdownItem,
	IGConnectContainer,
	TabRow,
	TabHeading,
	SortBox,
	RegularText,
	WhiteText,
	SectionText,
	CodeSnippetDiv,
	SelectionContainer,
	SelectionButton,
	SelectionAddOn,
	H2,
	PlanOption,
	SelectionInfo,
	OriginalPrice,
	DiscountedPrice,
	ModalSectionFullHeight,
	Bold,
	ApProFeatures,
	TrialTag,
	ProFeatures,
	LinkDiv,
	BlueBox,
	LinkText,
	StarIcon,
	PlanContainer,
	BillingFrequencyContainer,
	ModalSectionContainer,
	BillingFrequencySlider,
	StripeFieldContainer,
	SelectPlanContainer,
	PlansPageDescription,
	PlansPageUl,
	VideoWrapper,
	FlexSwitchRow,
	FlexSwitchCol,
	PortraitBox,
	LandscapeBox,
	FlexRow,
	ColorBoxContainer,
	BasicAccountSection,
	ListTabHeading,
	ListTabRow,
	ListTabArrow,
	ColorBox,
	ColorInput,
	EyeDropperIcon,
	DesktopRow,
	MobileRow,
	Container,
	Section,
	SectionHeader,
	SectionTitle,
	SectionDescription,
	AvatarGrid,
	AvatarCard,
	AvatarImage,
	CustomAvatarSection,
	UploadTitle,
	UploadDescription,
	TeleprompterBox,
	TeleprompterTextArea,
	FormGroup,
	Label,
	CenterDiv,
	VoiceLabel,
	VoiceLabelBox,
	Select,
	SmallSelect,
	SmallSelectDiv,
	SmallSelectButton,
	TextArea,
	ActionButton,
	LoadingOverlay,
	LoadingContent,
	LoadingTitle,
	LoadingDescription,
	ButtonGroup,
	PreviewImage,
	PreviewImageBox,
	VoiceUploadBox,
	UploadIconImg,
	UploadIconDiv,
	PhotoSample,
	TextImage,
	StepsSection,
	StepCircle,
	StepText,
	StepLine,
	StyledProgressBar,
	CharactersLimit,
	LimitReached,
	TableCell,
	VideoPreview,
	FlexCenter,
	RecordingTime,
	RecordingText,
	FlexGrid,
	LoadingBar,
	LoadingBarProgress
};
