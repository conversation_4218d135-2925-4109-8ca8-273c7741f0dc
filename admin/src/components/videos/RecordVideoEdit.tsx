import React, { useState } from "react";
import LabeledTextInput from "../inputs/LabeledTextInput";
import { useTranslation } from "../hooks/translations";

interface Props {
	setIsOnEditPage: (val: boolean) => void;
}

/* eslint-disable @typescript-eslint/no-unused-vars */
const RecordVideoEdit: React.FC<Props> = ({ setIsOnEditPage }) => {
	const [videoTitle, setVideoTitle] = useState("");
	const translation = useTranslation();

	const handleTitleChange = (title: string) => {
		setVideoTitle(title);
	};

	return (
		<>
			<LabeledTextInput
				label={translation.recordVideo.videoTitle}
				value={videoTitle}
				onChange={handleTitleChange}
				placeholder={translation.recordVideo.enterTitle}
			/>
		</>
	);
};

export default RecordVideoEdit;
