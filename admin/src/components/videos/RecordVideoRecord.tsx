import React, { useEffect, useState } from "react";
import { Flex, FormText, MainButton, PageSection } from "@src/styles/components";
import InputDevicesSelect from "../inputs/InputDevicesSelect";
import VideoTeleprompter from "./VideoTeleprompter";

interface Props {
	setIsOnEditPage: (val: boolean) => void;
}

enum displayMode {
	CAMERA_ONLY = "camera_only",
	SCREEN_ONLY = "screen_only",
	CAMERA_SCREEN = "camera_screen"
}

/* eslint-disable @typescript-eslint/no-unused-vars */
const RecordVideoRecord: React.FC<Props> = ({ setIsOnEditPage }) => {

	const [videoStream, setVideoStream] = useState<MediaStream | undefined>();
	const [audioStream, setAudioStream] = useState<MediaStream | undefined>();
	const [screenStream, setScreenStream] = useState<MediaStream | undefined>();

	const [mode, setMode] = useState<displayMode>(displayMode.CAMERA_ONLY);
	const [isPortrait, setIsPortrait] = useState(false);
	const [isRecording, setIsRecording] = useState(false);
	const [isPaused, setIsPaused] = useState(false);

	return (
		<>
			<Flex>
				<MainButton
					onClick={() => setMode(displayMode.CAMERA_ONLY)}
				>Show Camera</MainButton>
				<MainButton
					onClick={() => setMode(displayMode.SCREEN_ONLY)}
				>Show Screen</MainButton>
				<MainButton
					onClick={() => setMode(displayMode.CAMERA_SCREEN)}
				>Show Both</MainButton>
				<MainButton
					onClick={() => setIsPortrait((prev) => !prev)}
				>SET PORTRAIT TO: {isPortrait ? "FALSE" : "TRUE"}</MainButton>
			</Flex>
			<Flex>
				<MainButton onClick={() => {
					setIsRecording(true);
					setIsPaused(false);
				}}>START</MainButton>
				<MainButton onClick={() => {
					setIsPaused(true);
				}}>PAUSE</MainButton>
				<MainButton onClick={() => {
					setIsPaused(false);
				}}>RESUME</MainButton>
				<MainButton onClick={() => {
					setIsRecording(false);
					setIsPaused(false);
				}}>STOP</MainButton>
			</Flex>
			<Flex style={{ justifyContent: "space-between", gap: "20px" }}>
				<PageSection style={{ flex: 2 }}>
					<VideoTeleprompter 
						videoStream={videoStream}
						audioStream={audioStream}
						screenStream={screenStream}

						isRecording={isRecording}
						isPaused={isPaused}

						recordMode={mode}
						isPortrait={isPortrait}

						teleprompterText={"HELLO"}
						showText={true}
						fontSize={16}
						scriptSpeed={2}
					/>
				</PageSection>
				<div style={{ flex: 1 }}>
					<InputDevicesSelect
						disabled={false}
						videoStream={videoStream}
						setVideoStream={setVideoStream}
						audioStream={audioStream}
						setAudioStream={setAudioStream}
						screenStream={screenStream}
						setScreenStream={setScreenStream}
					/>
				</div>
			</Flex>
		</>
	);
};

export default RecordVideoRecord;
