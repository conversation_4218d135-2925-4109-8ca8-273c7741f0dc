import React, { useEffect, useState } from "react";
import { Flex, FormText, MainButton, PageSection } from "@src/styles/components";
import InputDevicesSelect from "../inputs/InputDevicesSelect";
import VideoTeleprompter from "./VideoTeleprompter";

interface Props {
	setIsOnEditPage: (val: boolean) => void;
}

enum displayMode {
	CAMERA_ONLY = "camera_only",
	SCREEN_ONLY = "screen_only",
	CAMERA_SCREEN = "camera_screen"
}

/* eslint-disable @typescript-eslint/no-unused-vars */
const RecordVideoRecord: React.FC<Props> = ({ setIsOnEditPage }) => {

	const [videoStream, setVideoStream] = useState<MediaStream | undefined>();
	const [audioStream, setAudioStream] = useState<MediaStream | undefined>();
	const [screenStream, setScreenStream] = useState<MediaStream | undefined>();

	const [mode, setMode] = useState<displayMode>(displayMode.CAMERA_ONLY);

	return (
		<>
			{/* <VideoTeleprompter></VideoTeleprompter> */}
			<Flex>
				<MainButton
					onClick={() => setMode(displayMode.CAMERA_ONLY)}
					disabled = {!videoStream}
				>Show Camera</MainButton>
				<MainButton
					onClick={() => setMode(displayMode.SCREEN_ONLY)}
					disabled={!screenStream}
				>Show Screen</MainButton>
				<MainButton
					onClick={() => setMode(displayMode.CAMERA_SCREEN)}
					disabled={!screenStream || !videoStream}
				>Show Both</MainButton>
			</Flex>
			<Flex style={{ justifyContent: "space-between", gap: "20px" }}>
				<PageSection style={{ flex: 2 }}>
					<VideoTeleprompter 
						videoStream={videoStream}
						audioStream={audioStream}
						screenStream={screenStream}

						isRecording={false}
						isPaused={false}
						hasStopped={false}

						displayMode={mode}
						isPortrait={false}

						teleprompterText={"HELLO"}
						showText={false}
						fontSize={16}
						scriptSpeed={2}

						isSaving={false}
					/>
				</PageSection>
				<div style={{ flex: 1 }}>
					<InputDevicesSelect
						disabled={false}
						videoStream={videoStream}
						setVideoStream={setVideoStream}
						audioStream={audioStream}
						setAudioStream={setAudioStream}
						screenStream={screenStream}
						setScreenStream={setScreenStream}
					/>
				</div>
			</Flex>
		</>
	);
};

export default RecordVideoRecord;
