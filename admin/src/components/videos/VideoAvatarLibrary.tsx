import React, { useEffect, useState } from "react";
import { Row, Col } from "react-bootstrap";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { useDropzone } from "react-dropzone";
import { useNavigate } from "react-router-dom";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import Lottie from "lottie-react";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";
import getVoices from "../utils/getVoices";
import getLooks from "../utils/getLooks";
import createLook from "../utils/createLook";
import generateAvatarVideo from "../utils/generateAvatarVideo";
import uploadCustomVoice from "../utils/uploadCustomVoice";
import getJobStatus from "../utils/getJobStatus";
import getVideoFile from "../utils/getVideoFile";
import { RecordVoiceModal } from "../modals/RecordVoiceModal";
import { ConfirmVoiceDelete } from "../modals/ConfirmVoiceDelete";
import { ConfirmLookDelete } from "../modals/ConfirmLookDelete";
import deleteAvatarVoice from "../utils/deleteAvatarVoice";
import deleteAvatarLook from "../utils/deleteAvatarLook";
import { getErrorString } from "../utils/getErrorString";
import {
	JobVideoStatus,
	JobsType
} from "@src/types/files";
import {
	TrashIcon,
	DeleteIcon
} from "@src/styles/forms";
import {
	Look,
	Voice,
	GenerateAvatarVideoOptions
} from "@src/types/avatar";
import {
	PageBody,
	ButtonIconImg,
	PreviewImageBox,
	VoiceUploadBox,
	LinkButton,
	CenterDiv,
	Container,
	SectionHeader,
	SectionTitle,
	SectionDescription,
	AvatarGrid,
	AvatarCard,
	AvatarImage,
	CustomAvatarSection,
	UploadTitle,
	UploadDescription,
	Label,
	VoiceLabelBox,
	TextArea,
	ActionButton,
	LoadingContent,
	LoadingTitle,
	LoadingDescription,
	VideoPlayer,
	PreviewImage,
	UploadIconImg,
	UploadIconDiv,
	PhotoSample,
	TextImage,
	BackButton,
	StepsSection,
	StepCircle,
	StepText,
	StepLine,
	Flex,
	CharactersLimit,
	LimitReached,
	VideoPreview,
	TableCell,
	FlexCenter,
	StyledProgressBar,
	ButtonIconDiv
} from "@src/styles/components";
import {
	GreenCheckIcon,
	NewLookIcon,
	XIcon,
	ArrowUploadIcon,
	GoodPhotos,
	BadPhotos,
	MicIcon
} from "@src/assets";
import SVG from "../svg/SVG";
import { useTheme } from "styled-components";
import { apTheme } from "@src/config/theme";

// eslint-disable-next-line max-lines-per-function
const VideoAvatarLibrary: React.FC = () => {
	const translation = useTranslation();
	const navigate = useNavigate();
	const characterLimit = 1000;
	const { apiRetryHandler } = useTokenCheck();
	const [myAvatars, setMyAvatars] = useState<Look[]>([]);
	const [voices, setVoices] = useState<Voice[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState("");
	const [selectedMyAvatar, setSelectedMyAvatar] = useState<Look | null>(null);
	const [selectedVoice, setSelectedVoice] = useState<Voice | null>(null);
	const [inputText, setInputText] = useState("");
	const [isGenerating, setIsGenerating] = useState(false);
	const [uploadedImage, setUploadedImage] = useState<File | null>(null);
	const [imagePreview, setImagePreview] = useState<string>("");
	const [groupId, setGroupId] = useState<string | null>(null);
	const [audioFile, setAudioFile] = useState<File | null>(null);
	const [name, setName] = useState("");
	const [uploading, setUploading] = useState(false);
	const [avatarProcessing, setAvatarProcessing] = useState(false);
	const [videoURL, setVideoURL] = useState<string | null>(null);
	const [videoId, setVideoId] = useState<string | null>(null);
	const [recordVoiceModal, setRecordVoiceModal] = useState(false);
	const [showDeleteVoiceModal, setShowDeleteVoiceModal] = useState(false);
	const [showDeleteLookModal, setShowDeleteLookModal] = useState<string | false>(false);
	const [avatarProgress, setAvatarProgress] = useState(0);
	const [isShortStep, setShortStep] = useState(false);
	const theme: apTheme = useTheme() as apTheme;

	useEffect(() => {
		const handleResize = () => {
			setShortStep(window.innerWidth < 775);
		};
		handleResize();
		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const handleCustomVoiceUpload = async (audioBlob: Blob) => {
		if (!audioBlob) {
			setError(translation.videoAvatarLibrary.voiceUploadMissingFields);
			return;
		}

		const audioFile = new File([audioBlob], "recorded_voice.mp3", { type: "audio/mpeg" });
		setAudioFile(audioFile);
	};

	const handleVoiceUpload = async () => {
		if (!audioFile) {
			setError(translation.videoAvatarLibrary.voiceUploadMissingFields);
			return;
		}
		const voiceName = name.trim() || "Custom Voice";

		setUploading(true);
		setError("");

		const { data: uploadResponse, error: uploadError } = await apiRetryHandler(() =>
			uploadCustomVoice(audioFile, voiceName)
		);
		setUploading(false);

		if (uploadError) {
			setError(uploadError);
		} else if (uploadResponse) {
			setVoices([
				...voices,
				uploadResponse
			]);
			setSelectedVoice(uploadResponse || null);
			setAudioFile(null);
			setName("");
		}
	};

	// Image upload dropzone
	const { getRootProps, getInputProps } = useDropzone({
		accept: {
			"image/*": [".jpeg", ".jpg", ".png"]
		},
		maxFiles: 1,
		onDrop: (acceptedFiles) => {
			if (acceptedFiles.length > 0) {
				resetAvatarSelect();
				const file = acceptedFiles[0];
				setUploadedImage(file);

				// Create preview
				const reader = new FileReader();
				reader.onload = () => {
					setImagePreview(reader.result as string);
				};
				reader.readAsDataURL(file);
			}
		}
	});

	// Voice upload dropzone
	const { getRootProps: getVoiceRootProps, getInputProps: getVoiceInputProps } = useDropzone({
		accept: {
			"audio/mpeg": [".mp3"],
			"audio/wav": [".wav"],
			"audio/x-m4a": [".m4a"]
		},
		maxFiles: 1,
		onDrop: (acceptedFiles) => {
			if (acceptedFiles.length > 0) {
				resetAvatarSelect();
				const file = acceptedFiles[0];
				setAudioFile(file);
				setName(file.name);
			}
		}
	});

	useEffect(() => {
		const fetchData = async () => {
			setLoading(true);
			try {
				const [myAvatarsResponse, voicesResponse] = await Promise.all([
					apiRetryHandler(() => getLooks()),
					apiRetryHandler(() => getVoices())
				]);

				if (!myAvatarsResponse.error && myAvatarsResponse.data) {
					setMyAvatars(myAvatarsResponse.data || []);
					setSelectedMyAvatar(myAvatarsResponse.data[0] || null);
					setGroupId(myAvatarsResponse.data[0]?.groupId || null);
				}

				if (!voicesResponse.error && voicesResponse.data) {
					setVoices(voicesResponse.data || []);
					setSelectedVoice(voicesResponse.data[0] || null);
				}
			} catch (err) {
				setError("Failed to load data");
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [apiRetryHandler]);


	const handleMyAvatarSelect = (myAvatar: Look) => {
		setSelectedMyAvatar(myAvatar);
		setUploadedImage(null);
		setImagePreview("");
	};

	const resetAvatarSelect = () => {
		setUploadedImage(null);
		setImagePreview("");
		setInputText("");
		setError("");
	};

	const refreshMyAvatars = async () => {
		try {
			const myAvatarsResponse = await apiRetryHandler(() => getLooks());
			if (myAvatarsResponse.data) {
				resetAvatarSelect();
				setMyAvatars(myAvatarsResponse.data || []);
				setSelectedMyAvatar(myAvatarsResponse.data[0] || null);
				setGroupId(myAvatarsResponse.data[0]?.groupId || null);
			}
		} catch (err) {
			setError(translation.videoAvatarLibrary.failedToLoadData);
		}
	};

	const handleCreateCustomAvatar = async () => {
		if (!uploadedImage) return;

		try {
			setIsGenerating(true);

			const { data: createResponse, error: createError } = await apiRetryHandler(() =>
				createLook(uploadedImage, groupId)
			);

			if (createError || !createResponse?._id) {
				setError(translation.videoAvatarLibrary.failedToCreateCustomAvatar);
				return;
			}

			await refreshMyAvatars();
		} catch (err) {
			setError(translation.videoAvatarLibrary.failedToCreateCustomAvatarWithReason + (err instanceof Error ? err.message : "Unknown error"));
		} finally {
			setIsGenerating(false);
		}
	};

	const mapEncodeProgress = (progressPercent: number) => {
		const clamped = Math.min(Math.max(progressPercent, 0), 100);
		return 57 + (clamped * 0.4);
	};

	const pollJobStatus = async (jobId: string) => {
		let fakeProgress = 0;
		let attempts = 0;
		// 30 minutes timeout
		const maxAttempts = 360;
		setAvatarProcessing(true);

		const checkStatus = async () => {
			try {
				const { data, error } = await apiRetryHandler(() => getJobStatus(jobId));

				if (error) {
					throw new Error(translation.videoAvatarLibrary.failedToCheckVideoStatus);
				}

				if (data.status === JobVideoStatus.FAILED) {
					setAvatarProgress(100);
					setError(translation.videoAvatarLibrary.failedToCheckVideoStatus);
					return;
				}

				if (data.status === JobVideoStatus.CREATED || (data.status === JobVideoStatus.RUNNING && data.type === JobsType.AVATAR_VIDEO)) {
					fakeProgress = Math.floor(Math.min(fakeProgress + 2, 57));
					setAvatarProgress(fakeProgress);
				}

				if (data.status === JobVideoStatus.RUNNING && data.type === JobsType.ENCODE_VIDEO) {
					const realProgress = mapEncodeProgress(data.progressPercent || 0);
					const smoothed = Math.max(avatarProgress, Math.floor(realProgress));
					setAvatarProgress(smoothed);
				}

				if (data.status === JobVideoStatus.COMPLETE) {
					setAvatarProgress(100);
					const { data: videoData } = await apiRetryHandler(async () => await getVideoFile(data.videoId));
					if (videoData.video) {
						setVideoURL(videoData.video.publicVideoURL);
						setVideoId(videoData.video._id);
					} else {
						setError(translation.videoAvatarLibrary.failedToCheckVideoStatus);
					}
				} else {
					attempts++;
					if (attempts < maxAttempts) {
						setTimeout(checkStatus, 5000);
					} else {
						setError(translation.videoAvatarLibrary.failedToCheckVideoStatus);
					}
				}
			} catch (err) {
				attempts++;
				if (attempts < maxAttempts) {
					setTimeout(checkStatus, 5000);
				} else {
					setAvatarProgress(0);
					setError(translation.videoAvatarLibrary.failedToCheckVideoStatus);
				}
			}
		};
		checkStatus();
	};

	// Handle video generation and submission
	const handleGenerateVideo = async () => {
		if (!inputText.trim() || !selectedVoice || !selectedMyAvatar) {
			setError(translation.videoAvatarLibrary.missingFields);
			return;
		}

		try {
			const options: GenerateAvatarVideoOptions = {
				voiceId: selectedVoice._id,
				lookId: selectedMyAvatar._id,
				text: inputText.trim()
			};

			setIsGenerating(true);
			const { data: generateResponse, error: generateError } = await apiRetryHandler(() => generateAvatarVideo(options));

			if (generateError || !generateResponse?.jobId) {
				setError(translation.videoAvatarLibrary.failedToGenerateVideo);
			} else {
				pollJobStatus(generateResponse.jobId);
			}

			setIsGenerating(false);
			resetAvatarSelect();
		} catch (err) {
			setIsGenerating(false);
			setError(translation.videoAvatarLibrary.failedToGenerateVideo);
		}
	};

	const handleVoiceDelete = async () => {
		if (!selectedVoice) return;
		setLoading(true);

		const { error } = await apiRetryHandler(async () => await deleteAvatarVoice(selectedVoice._id));

		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setError(errorText);
		} else {
			setVoices([]);
			resetAvatarSelect();
		}

		setShowDeleteVoiceModal(false);
		setLoading(false);
	};

	const handleLookDelete = async (id: string) => {
		if (!selectedMyAvatar) return;
		setLoading(true);

		const { error } = await apiRetryHandler(async () => await deleteAvatarLook(id));

		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setError(errorText);
		} else {
			await refreshMyAvatars();
		}

		setShowDeleteLookModal(false);
		setLoading(false);
	};

	if (loading) {
		return (
			<Container>
				<LoadingContent>
					<Lottie
						animationData={LoadingSpinnerAnimation}
						style={{ width: 100, height: 100, margin: "auto" }}
						loop={true}
					/>
					<LoadingTitle>{translation.videoAvatarLibrary.loadingTitle}</LoadingTitle>
					<LoadingDescription>{translation.videoAvatarLibrary.loadingDescription}</LoadingDescription>
				</LoadingContent>
			</Container>
		);
	}

	return (
		<PageBody>
			{error && <ErrorMessage error={error} setError={setError} displayCloseIcon={true} style={{ marginBottom: "1rem", textAlign: "center" }} />}
			<SectionHeader>
				<SectionTitle>
					{myAvatars.length > 0 && voices.length > 0 ? translation.videoAvatarLibrary.avatarVideoCreator : translation.videoAvatarLibrary.myAvatarsTitle}
				</SectionTitle>
				<SectionDescription>{translation.videoAvatarLibrary.myAvatarsDescription}</SectionDescription>
			</SectionHeader>

			<Row pl="0" pr="0">
				<Col sm="12" md="12" lg="12" xl="8">
					<StepsSection>
						<StepCircle active={true}>1</StepCircle>
						<StepText active={true}>
							{isShortStep ? translation.videoAvatarLibrary.photoUploadShort : translation.videoAvatarLibrary.photoUpload}
						</StepText>
						<StepLine active={myAvatars.length > 0 || imagePreview ? true : false} />

						<StepCircle active={myAvatars.length > 0}>2</StepCircle>
						<StepText active={myAvatars.length > 0}>
							{isShortStep ? translation.videoAvatarLibrary.addVoiceShort : translation.videoAvatarLibrary.addVoice}
						</StepText>
						<StepLine active={myAvatars.length > 0 && audioFile || voices.length > 0 ? true : false} />

						<StepCircle active={myAvatars.length > 0 && voices.length > 0}>3</StepCircle>
						<StepText active={myAvatars.length > 0 && voices.length > 0}>
							{isShortStep ? translation.videoAvatarLibrary.generateAvatarVideoShort : translation.videoAvatarLibrary.generateAvatarVideo}
						</StepText>
					</StepsSection>
				</Col>
				<Col sm="12" md="12" lg="12" xl="4">
					{myAvatars.length > 0 && voices.length > 0 && selectedMyAvatar && !avatarProcessing && (
						<Flex style={{ float: "right" }}>
							<Label style={{ margin: "0 10px 0 0" }}>{translation.videoAvatarLibrary.yourVoice}</Label>
							<VoiceLabelBox>
								<ButtonIconImg
									src={GreenCheckIcon}
									style={{ position: "relative", marginRight: "10px" }}
								/>
								{voices[0].name}
								<TrashIcon
									style={{ position: "relative", marginLeft: "15px", height: "16px" }}
									onClick={() => setShowDeleteVoiceModal(true)}
								/>
							</VoiceLabelBox>
						</Flex>
					)}
				</Col>
			</Row>

			{/* step 1 */}
			{myAvatars.length < 1 && !avatarProcessing && (
				<Row pl="0" pr="0" className="mt-4">
					<Col sm="12" md="12" lg="12" xl="7" className="mb-4">
						<CustomAvatarSection center={true} border={true}>
							{imagePreview ? (
								<div>
									<div>
										<UploadDescription><b>{translation.videoAvatarLibrary.step1}</b></UploadDescription>
										<UploadTitle>
											{translation.videoAvatarLibrary.uploadYourPhoto}
										</UploadTitle>
										<AvatarCard
											selected={false}
											style={{ cursor: "default" }}
										>
											<AvatarImage
												src={imagePreview}
												alt="imagePreview"
											/>
										</AvatarCard>

										<ActionButton
											onClick={handleCreateCustomAvatar}
											disabled={isGenerating}
											style={{ margin: "1rem 0" }}
										>
											{isGenerating ? (
												<>
													<Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "25px", display: "inline-block", marginRight: "10px" }} />
													{translation.videoAvatarLibrary.uploading}
												</>
											) : (
												translation.videoAvatarLibrary.uploadImage
											)}
										</ActionButton>
										<br />
										{isGenerating && (
											<p>{translation.videoAvatarLibrary.waitingForAvatar} <br/> <b>{translation.videoAvatarLibrary.takeYourTime}</b></p>
										)}
										{!isGenerating && (
											<BackButton
												onClick={(e) => {
													e.preventDefault();
													resetAvatarSelect();
												}}
												className="mt-1 mx-auto"
											>
												{translation.general.cancel}
											</BackButton>
										)}
									</div>
								</div>
							) : (
								<div {...getRootProps()}>
									<input {...getInputProps()} />
									<UploadIconDiv>
										<SVG src={ArrowUploadIcon} />
									</UploadIconDiv>
									<UploadDescription><b>{translation.videoAvatarLibrary.step1}</b></UploadDescription>
									<UploadTitle>
										{translation.videoAvatarLibrary.dragDropPhoto}
									</UploadTitle>
									<UploadDescription>
										{translation.videoAvatarLibrary.dragDropImage}
										<br />
										{translation.videoAvatarLibrary.supportedFormats}
									</UploadDescription>
									<ActionButton>
										{translation.videoAvatarLibrary.selectPhoto}
									</ActionButton>
								</div>
							)}
						</CustomAvatarSection>
					</Col>
					<Col sm="12" md="12" lg="12" xl="5" className="mb-4">
						<CustomAvatarSection style={{ textAlign: "left" }}>
							<UploadTitle>{translation.videoAvatarLibrary.photoRequirements}</UploadTitle>
							<h5>
								<TextImage src={GreenCheckIcon}/>
								<b>{translation.videoAvatarLibrary.goodPhotos}</b>
							</h5>
							<p>{translation.videoAvatarLibrary.goodPhotosText}</p>
							<PhotoSample src={GoodPhotos} style={{ marginBottom: "1.5rem" }}/>
							<h5>
								<TextImage src={XIcon}/>
								<b>{translation.videoAvatarLibrary.badPhotos}</b>
							</h5>
							<p>{translation.videoAvatarLibrary.badPhotosText}</p>
							<PhotoSample src={BadPhotos}/>
						</CustomAvatarSection>
					</Col>
				</Row>
			)}

			{/* step 2 */}
			{myAvatars.length > 0 && voices.length < 1 && !avatarProcessing && (
				<Row pl="0" pr="0" className="mt-4">
					<Col sm="12" md="12" lg="12" xl="7" className="mb-4">
						<CustomAvatarSection center={true}>
							<div>
								<div>
									<UploadDescription><b>{translation.videoAvatarLibrary.step1}</b></UploadDescription>
									<UploadTitle>
										{translation.videoAvatarLibrary.uploadYourPhoto}
									</UploadTitle>
									<AvatarCard
										key={myAvatars[0]._id}
										selected={true}
										style={{ cursor: "default" }}
									>
										<DeleteIcon
											onClick={(e) => {
												e.stopPropagation();
												setShowDeleteLookModal(myAvatars[0]._id);
											}}
										/>
										<AvatarImage
											src={myAvatars[0].imageUrl}
											alt={myAvatars[0].name}
										/>
									</AvatarCard>
								</div>
							</div>
						</CustomAvatarSection>
					</Col>
					<Col sm="12" md="12" lg="12" xl="5" className="mb-4">
						<CustomAvatarSection border={true} center={true}>
							<div>
								<UploadDescription><b>{translation.videoAvatarLibrary.step2}</b></UploadDescription>
								<UploadTitle>{translation.videoAvatarLibrary.uploadYourVoice}</UploadTitle>
								{!audioFile && (
									<>
										<UploadDescription>
											{translation.videoAvatarLibrary.dragDropVoice}
											<br />
											<b>{translation.videoAvatarLibrary.voiceSupportedFormats}</b>
										</UploadDescription>
										<FlexCenter style={{ margin: "2rem 0" }}>
											<div {...getVoiceRootProps()}>
												<input {...getVoiceInputProps()} />
												<ActionButton>
													<ButtonIconDiv>
														<SVG src={ArrowUploadIcon} />
													</ButtonIconDiv>
													{translation.general.upload}
												</ActionButton>
											</div>
											<UploadDescription style={{ textTransform: "uppercase", margin: "0 1rem" }}>{translation.general.or}</UploadDescription>
											<ActionButton
												onClick={() => setRecordVoiceModal(true)}
												variant="red"
											>
												<ButtonIconDiv>
													<SVG src={MicIcon} fill={theme.colors.apWhite}/>
												</ButtonIconDiv>
												{translation.general.record}
											</ActionButton>
										</FlexCenter>
									</>
								)}

								{audioFile ? (
									<>
										<VoiceUploadBox>
											{audioFile.name}
											{!uploading && (
												<ButtonIconImg
													onClick={(e) => {
														e.preventDefault();
														setAudioFile(null);
													}}
													src={XIcon}
													alt={translation.videoAvatarLibrary.reset}
													style={{ position: "relative", marginLeft: "10px" }}
												/>
											)}
										</VoiceUploadBox>

										<ActionButton onClick={handleVoiceUpload} disabled={uploading} style={{ minWidth: 180, margin: "1rem 0" }}>
											{uploading ? (
												<>
													<Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "25px", display: "inline-block", marginRight: "10px" }} />
													{translation.videoAvatarLibrary.savingVoice}
												</>
											) : (
												translation.videoAvatarLibrary.uploadVoice
											)}
										</ActionButton>
										<br />
										{!uploading && (
											<BackButton
												onClick={(e) => {
													e.preventDefault();
													setAudioFile(null);
												}}
												className="mt-1 mx-auto"
											>
												{translation.general.cancel}
											</BackButton>
										)}
									</>
								) : (
									<CenterDiv>
										<UploadDescription style={{ fontSize: "0.8rem" }}>{translation.videoAvatarLibrary.audioGuidelines}</UploadDescription>
									</CenterDiv>
								)}
							</div>
						</CustomAvatarSection>
					</Col>
				</Row>
			)}

			{/* step 3 */}
			{myAvatars.length > 0 && voices.length > 0 && selectedMyAvatar && !avatarProcessing && (
				<Row pl="0" pr="0">
					<Col sm="12" md="12" lg="12" xl="7" className="mb-4">
						<SectionDescription blackColor={true}>
							{translation.videoAvatarLibrary.avatarVideoText1}
							<br/>
							{translation.videoAvatarLibrary.avatarVideoText2}
							<LinkButton onClick={() => navigate("/video-library")}>{translation.videoAvatarLibrary.videoLibrary}</LinkButton>
						</SectionDescription>

						<Label style={{ marginTop: "2rem" }}>{translation.videoAvatarLibrary.enterYourText}</Label>
						<TextArea
							id="VideoTextArea"
							value={inputText}
							onChange={(e) => setInputText(e.target.value)}
							placeholder={translation.videoAvatarLibrary.textPlaceholder}
							rows={6}
						/>
						<CharactersLimit>
							{inputText.length > characterLimit ? (
								<LimitReached>
									{inputText.length}
								</LimitReached>
							) : (inputText.length)}
							{`/${characterLimit} ${translation.general.characters}`}
						</CharactersLimit>

						<ActionButton
							onClick={handleGenerateVideo}
							disabled={isGenerating || inputText.length > characterLimit || !inputText.trim() || !selectedVoice || !selectedMyAvatar}
							style={{ marginTop: "2rem" }}
						>
							{translation.videoAvatarLibrary.generateVideo}
						</ActionButton>
					</Col>
					<Col sm="12" md="12" lg="12" xl="5" className="mb-4">
						<CustomAvatarSection scroll={true}>
							<AvatarGrid>
								<AvatarCard>
									{!imagePreview ? (
										<CustomAvatarSection {...getRootProps()} style={{ padding: "2.3rem", height: "100%" }}>
											<input {...getInputProps()} />
											<ButtonIconDiv style={{ position: "relative", marginLeft: "10px" }}>
												<SVG src={NewLookIcon} fill={theme.colors.apTextColor}/>
											</ButtonIconDiv>
											<UploadTitle>{translation.videoAvatarLibrary.addNewLook}</UploadTitle>
											<UploadDescription>
												{translation.videoAvatarLibrary.addMorePhotos}
											</UploadDescription>
										</CustomAvatarSection>
									) : (
										<>
											<PreviewImageBox style={{ marginTop: "1rem" }}>
												{!isGenerating && (
													<ButtonIconImg
														onClick={(e) => {
															e.preventDefault();
															resetAvatarSelect();
														}}
														src={XIcon}
														alt={translation.videoAvatarLibrary.reset}
														style={{ position: "relative", marginLeft: "-25px", marginTop: "5px", float: "right" }}
													/>
												)}
												<PreviewImage src={imagePreview} alt="Preview" />
											</PreviewImageBox>

											<ActionButton onClick={handleCreateCustomAvatar} disabled={isGenerating} style={{ padding: "5px", marginBottom: "20px" }}>
												{isGenerating
													? (
														<>
															<Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "25px", display: "inline-block", marginRight: "10px" }} />
															{translation.videoAvatarLibrary.uploading}
														</>
													) : translation.videoAvatarLibrary.addLook}
											</ActionButton>
										</>
									)}
								</AvatarCard>
								{myAvatars.map((myAvatar) => (
									<AvatarCard
										key={myAvatar._id}
										selected={selectedMyAvatar?._id === myAvatar._id}
										onClick={() => handleMyAvatarSelect(myAvatar)}
									>
										<DeleteIcon
											onClick={(e) => {
												e.stopPropagation();
												setShowDeleteLookModal(myAvatar._id);
											}}
										/>
										<AvatarImage
											src={myAvatar.imageUrl}
											alt={myAvatar.name}
										/>
									</AvatarCard>
								))}
							</AvatarGrid>
						</CustomAvatarSection>
					</Col>
				</Row>
			)}

			{/* step 4: waiting for video generation */}
			{(avatarProcessing || videoURL) && (
				<Row pl="0" pr="0" className="mt-4">
					<Col sm="12" md="12" lg="12" xl="7" className="mb-4">
						<CustomAvatarSection center={true}>
							{videoId ? (
								<div>
									<UploadIconImg src={GreenCheckIcon}/>
									<UploadTitle> {translation.videoAvatarLibrary.videoDone}</UploadTitle>
									<p>{translation.videoAvatarLibrary.addButtons}</p>
									<ActionButton onClick={() => navigate(`/create-video?videoId=${videoId}`)} style={{ marginBottom: "1rem" }}>
										{translation.general.continue}
									</ActionButton>
									<p>{translation.general.or}</p>
									<LinkButton
										onClick={() => {
											setAvatarProcessing(false);
											setVideoURL(null);
											setVideoId(null);
										}}
									>
										{translation.videoAvatarLibrary.createAnother}
									</LinkButton>
								</div>
							) : (
								<div>
									<UploadTitle> {translation.videoAvatarLibrary.generatingVideo}</UploadTitle>
									<br />
									{translation.videoAvatarLibrary.leavePageText}
									<LinkButton onClick={() => navigate("/video-library")}>{translation.videoAvatarLibrary.videoLibrary}</LinkButton>
									{translation.videoAvatarLibrary.leavePageText2}
								</div>
							)}
						</CustomAvatarSection>
					</Col>
					<Col sm="12" md="12" lg="12" xl="5" className="mb-4">
						<CustomAvatarSection center={true}>
							{videoURL ? (
								<VideoPreview>
									<VideoPlayer
										src={videoURL}
										autoPlay={false}
										controls={true}
										playsInline={true}
										loop={false}
										muted={false}
										crossOrigin="anonymous"
										webkit-playsinline=""
										x5-playsinline=""
									/>
								</VideoPreview>
							) : (
								<VideoPreview background={true} imageUrl={selectedMyAvatar?.imageUrl} >
									<TableCell>
										<div><b>{`${avatarProgress}%`}</b></div>
										<StyledProgressBar now={avatarProgress}/>
									</TableCell>
								</VideoPreview>
							)}
						</CustomAvatarSection>
					</Col>
				</Row>
			)}
			<RecordVoiceModal visible={recordVoiceModal} onCancel={() => setRecordVoiceModal(false)} onSubmit={handleCustomVoiceUpload} />
			<ConfirmVoiceDelete visible={showDeleteVoiceModal} onCancel={() => setShowDeleteVoiceModal(false)} onContinue={handleVoiceDelete} />
			<ConfirmLookDelete
				visible={!!showDeleteLookModal}
				onCancel={() => setShowDeleteLookModal(false)}
				onContinue={() => {
					if (showDeleteLookModal) handleLookDelete(showDeleteLookModal);
				}}
			/>
		</PageBody>
	);
};

export default VideoAvatarLibrary;
