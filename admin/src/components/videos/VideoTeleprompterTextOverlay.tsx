import { WhiteText } from "@src/styles/components";
import React, { useEffect, useState } from "react";

interface Props {
	teleprompterText: string;
	scrollSpeed: number;
	fontSize: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, scrollSpeed, fontSize, scrollText }) => {

	const [splitText, setSplitText] = useState<string[]>();
	const [activeText, setActiveText] = useState<string>();

	const splitTeleprompterText = (wordsPerSplit: number) => {

	};

	useEffect(() => {
		splitTeleprompterText(10);
	}, [teleprompterText]);
	
	return <div style={{
		position: "absolute",
		backgroundColor: "rgba(0, 0, 0, 0.5)",
		top: 0,
		left: "50%",
		transform: "translateX(-50%)",
		margin: "10px 0",
		padding: "10px",
		boxSizing: "border-box"
	}}>
		<WhiteText style={{
			top: 0,
			margin: 0,
			fontSize: `${fontSize}px`
		}}>
			{teleprompterText}
		</WhiteText>
	</div>;
}

export default TeleprompterTextOverlay;
