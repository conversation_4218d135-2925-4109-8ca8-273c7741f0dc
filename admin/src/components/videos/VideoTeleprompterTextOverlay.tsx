import { Flex, WhiteText } from "@src/styles/components";
import React, { useEffect, useState } from "react";

interface Props {
	teleprompterText: string;
	fontSize: number;
	scrollSpeed: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, fontSize, scrollSpeed, scrollText }) => {

	const [splitText, setSplitText] = useState<string[]>();
	const [activeText, setActiveText] = useState<string>();
	const [wordsPerSplit, setWordsPerSplit] = useState<number>();

	const splitTeleprompterText = () => {
		if (!teleprompterText.trim() || !wordsPerSplit || wordsPerSplit <= 0) {
			setSplitText([]);
			return;
		}

		console.log(wordsPerSplit)
		
		// First split by newlines, then by spaces, treating each \n as a separate word
		const words = teleprompterText.trim()
			.split(/(\n)/)
			.flatMap(part => part === '\n' ? ['\n'] : part.split(/ +/).filter(word => word.length > 0))
			.filter(word => word.length > 0);
		const chunks: string[] = [];
		let i = 0;

		while (i < words.length) {
			let endIndex = Math.min(i + wordsPerSplit, words.length);

			if (endIndex < words.length) {
				// Continue adding words until a word that ends with a period is found or we hit the extra word count limit
				let extraWordCount = 0;
				const extraWordCountLimit = wordsPerSplit / 3;
				while (extraWordCount < extraWordCountLimit && endIndex < words.length && !words[endIndex - 1].endsWith('.')) {
					endIndex++;
					extraWordCount++;
				}
			}

			const chunk = words.slice(i, endIndex).join(' ');
			chunks.push(chunk);
			i = endIndex;
		}

		setSplitText(chunks);
	};

	useEffect(() => {
		setWordsPerSplit(Math.round(50 * (16 / fontSize)));
	}, [fontSize]);

	useEffect(() => {
		splitTeleprompterText();
	}, [teleprompterText, wordsPerSplit]);

	useEffect(() => {
		if (splitText && splitText.length > 0) {
			console.log(splitText)
			setActiveText(splitText[0]);
		}
	}, [splitText]);

	return (
		<Flex style={{ flexDirection: "column", alignItems: "center" }}>
			<div style={{
				position: "absolute",
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				top: 0,
				margin: "10px 10px",
				padding: "10px",
				boxSizing: "border-box",
				borderRadius: "10px"
			}}>
				<WhiteText style={{
					margin: 0,
					fontSize: `${fontSize}px`,
					whiteSpace: "pre-line"
				}}>
					{activeText}
				</WhiteText>
			</div>
		</Flex>
	);
}

export default TeleprompterTextOverlay;
