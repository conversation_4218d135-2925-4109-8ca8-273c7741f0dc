import React from "react";

interface Props {
	teleprompterText: string;
	scrollSpeed: number;
	fontSize: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, scrollSpeed, fontSize, scrollText }) => {
	return <div style={{
		position: "absolute",
		backgroundColor: "rgba(0, 0, 0, 0.5)",
		top: 0,
		left: "50%",
		transform: "translateX(-50%)",
		margin: "10px 0",
		padding: "10px",
		boxSizing: "border-box"
	}}>
		<p style={{
			color: "white",
			top: 0,
			textAlign: "center",
			margin: 0
		}}>SAMPLE TELEPROMPT</p>
	</div>;
}

export default TeleprompterTextOverlay;
