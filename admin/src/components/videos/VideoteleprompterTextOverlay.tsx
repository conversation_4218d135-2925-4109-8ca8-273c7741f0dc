import { Flex, WhiteText } from "@src/styles/components";
import React, { useEffect, useState } from "react";

interface Props {
	teleprompterText: string;
	fontSize: number;
	scrollSpeed: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, fontSize, scrollSpeed, scrollText }) => {

	const [splitText, setSplitText] = useState<string[]>();
	const [activeText, setActiveText] = useState<string>();
	const [wordsPerSplit, setWordsPerSplit] = useState<number>();

	const splitTeleprompterText = () => {
		if (!teleprompterText.trim() || !wordsPerSplit || wordsPerSplit <= 0) {
			setSplitText([]);
			return;
		}

		const words = teleprompterText.trim().split(/\s+/);
		const chunks: string[] = [];

		for (let i = 0; i < words.length; i += wordsPerSplit) {
			let chunk: string;
			if(i + wordsPerSplit < words.length){
				chunk = words.slice(i, i + wordsPerSplit).join(' ');
			} else {
				chunk = words.slice(i).join(' ');
			}
			chunks.push(chunk);
		}

		setSplitText(chunks);
	};

	useEffect(() => {
		setWordsPerSplit(Math.round(fontSize * 3));
	}, [fontSize]);

	useEffect(() => {
		splitTeleprompterText();
	}, [teleprompterText, wordsPerSplit]);

	useEffect(() => {
		if (splitText && splitText.length > 0) {
			console.log(5)
			setActiveText(splitText[0]);
		}
	}, [splitText])

	return <Flex style={{flexDirection: "column", alignItems: "center"}}>
		<div style={{
			position: "absolute",
			backgroundColor: "rgba(0, 0, 0, 0.5)",
			top: 0,
			margin: "10px 10px",
			padding: "10px",
			boxSizing: "border-box",
		}}>
			<WhiteText style={{
				margin: 0,
				fontSize: `${fontSize}px`,
				whiteSpace: "pre-line"
			}}>
				{activeText}
			</WhiteText>
		</div>
	</Flex>;
}

export default TeleprompterTextOverlay;
