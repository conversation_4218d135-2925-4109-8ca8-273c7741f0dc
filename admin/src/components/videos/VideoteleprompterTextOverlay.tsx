import { WhiteText } from "@src/styles/components";
import React, { useEffect, useState } from "react";

interface Props {
	teleprompterText: string;
	fontSize: number;
	scrollSpeed: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, fontSize, scrollSpeed, scrollText }) => {

	const [splitText, setSplitText] = useState<string[]>();
	const [activeText, setActiveText] = useState<string>();
	const [wordsPerSplit, setWordsPerSplit] = useState(0);

	const splitTeleprompterText = () => {
		if (!teleprompterText.trim()) {
			setSplitText([]);
			return;
		}

		const words = teleprompterText.trim().split(/\s+/);
		console.log(words)
		const chunks: string[] = [];

		for (let i = 0; i < words.length; i += wordsPerSplit) {
			let chunk: string;
			if(i + wordsPerSplit < words.length)
			const chunk = words.slice(i, Math.min(i + wordsPerSplit, words.length-1)).join(' ');
			chunks.push(chunk);
		}

		setSplitText(chunks);
	};

	useEffect(() => {
		setWordsPerSplit(Math.round(fontSize * 3));
	}, [fontSize]);

	useEffect(() => {
		console.log(4)
		splitTeleprompterText();
	}, [teleprompterText, wordsPerSplit]);

	useEffect(() => {
		if (splitText && splitText.length > 0) {
			setActiveText(splitText[0]);
		}
	}, [splitText])

	// return <div style={{
	// 	position: "absolute",
	// 	backgroundColor: "rgba(0, 0, 0, 0.5)",
	// 	top: 0,
	// 	left: "50%",
	// 	transform: "translateX(-50%)",
	// 	margin: "10px 0",
	// 	padding: "10px",
	// 	boxSizing: "border-box"
	// }}>
	// 	<WhiteText style={{
	// 		top: 0,
	// 		margin: 0,
	// 		fontSize: `${fontSize}px`
	// 	}}>
	// 		{activeText}
	// 	</WhiteText>
	// </div>;

	return <></>
}

export default TeleprompterTextOverlay;
