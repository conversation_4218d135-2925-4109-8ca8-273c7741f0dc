import { Flex, WhiteText } from "@src/styles/components";
import React, { useEffect, useState } from "react";

interface Props {
	teleprompterText: string;
	fontSize: number;
	scrollSpeed: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, fontSize, scrollSpeed, scrollText }) => {

	const [splitText, setSplitText] = useState<string[]>();
	const [activeText, setActiveText] = useState<string>();
	const [wordsPerSplit, setWordsPerSplit] = useState<number>();

	const splitTeleprompterText = () => {
		if (!teleprompterText.trim() || !wordsPerSplit || wordsPerSplit <= 0) {
			setSplitText([]);
			return;
		}

		const words = teleprompterText.trim().split(/ +/);
		const chunks: string[] = [];
		let i = 0;

		while (i < words.length) {
			let endIndex = Math.min(i + wordsPerSplit, words.length);

			// If we haven't reached the end of all words, look for a period
			if (endIndex < words.length) {
				// Continue adding words until a that ends with a period is found
				let extraWordCount = 0
				while (extraWordCount < wordsPerSplit / 3 && endIndex < words.length && !words[endIndex - 1].endsWith('.')) {
					endIndex++;
					extraWordCount++;
				}
			}

			const chunk = words.slice(i, endIndex).join(' ');
			chunks.push(chunk);
			i = endIndex;
		}

		setSplitText(chunks);
	};

	useEffect(() => {
		setWordsPerSplit(Math.round(fontSize * 3));
	}, [fontSize]);

	useEffect(() => {
		splitTeleprompterText();
	}, [teleprompterText, wordsPerSplit]);

	useEffect(() => {
		if (splitText && splitText.length > 0) {
			console.log(splitText)
			setActiveText(splitText[0]);
		}
	}, [splitText]);

	return (
		<Flex style={{ flexDirection: "column", alignItems: "center" }}>
			<div style={{
				position: "absolute",
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				top: 0,
				margin: "10px 10px",
				padding: "10px",
				boxSizing: "border-box",
				borderRadius: "10px"
			}}>
				<WhiteText style={{
					margin: 0,
					fontSize: `${fontSize}px`,
					whiteSpace: "pre-line"
				}}>
					{activeText}
				</WhiteText>
			</div>
		</Flex>
	);
}

export default TeleprompterTextOverlay;
