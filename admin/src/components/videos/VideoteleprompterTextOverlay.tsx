import { WhiteText } from "@src/styles/components";
import React, { useEffect, useState } from "react";

interface Props {
	teleprompterText: string;
	fontSize: number;
	scrollSpeed: number;
	scrollText: boolean;
}

const TeleprompterTextOverlay: React.FC<Props> = ({ teleprompterText, fontSize, scrollSpeed, scrollText }) => {

	const [splitText, setSplitText] = useState<string[]>();
	const [activeText, setActiveText] = useState<string>();
	const [wordsPerSplit, setWordsPerSplit] = useState<number>();

	const splitTeleprompterText = () => {
		if (!teleprompterText.trim() || !wordsPerSplit || wordsPerSplit <= 0) {
			setSplitText([]);
			return;
		}

		const words = teleprompterText.trim().split(/\s+/);
		console.log('words:', words);
		console.log('wordsPerSplit:', wordsPerSplit);

		const chunks: string[] = [];
		let iterationCount = 0;
		const maxIterations = Math.ceil(words.length / wordsPerSplit) + 1; // Safety limit

		for (let i = 0; i < words.length; i += wordsPerSplit) {
			iterationCount++;

			// Safety check to prevent infinite loops
			if (iterationCount > maxIterations) {
				console.error('Infinite loop detected in splitTeleprompterText');
				break;
			}

			console.log(`Iteration ${iterationCount}: i=${i}, wordsPerSplit=${wordsPerSplit}`);

			let chunk: string;
			if(i + wordsPerSplit < words.length){
				chunk = words.slice(i, i + wordsPerSplit).join(' ');
			} else {
				chunk = words.slice(i).join(' ');
			}

			console.log('chunk:', chunk);
			chunks.push(chunk);
		}

		console.log('final chunks:', chunks);
		setSplitText(chunks);
	};

	useEffect(() => {
		setWordsPerSplit(Math.round(fontSize * 3));
	}, [fontSize]);

	useEffect(() => {
		console.log(4)
		splitTeleprompterText();
	}, [teleprompterText, wordsPerSplit]);

	useEffect(() => {
		if (splitText && splitText.length > 0) {
			setActiveText(splitText[0]);
		}
	}, [splitText])

	// return <div style={{
	// 	position: "absolute",
	// 	backgroundColor: "rgba(0, 0, 0, 0.5)",
	// 	top: 0,
	// 	left: "50%",
	// 	transform: "translateX(-50%)",
	// 	margin: "10px 0",
	// 	padding: "10px",
	// 	boxSizing: "border-box"
	// }}>
	// 	<WhiteText style={{
	// 		top: 0,
	// 		margin: 0,
	// 		fontSize: `${fontSize}px`
	// 	}}>
	// 		{activeText}
	// 	</WhiteText>
	// </div>;

	return <></>
}

export default TeleprompterTextOverlay;
