import { Flex, <PERSON>Button, SemiTransparentOverlay, StyledProgressBar, WhiteText } from "@src/styles/components";
import React, { useEffect, useRef, useState } from "react";
import { freeMediaStream } from "../utils/freeMediaStream";
import MissingDevice from "../common/MissingDevice";
import TeleprompterTextOverlay from "./VideoteleprompterTextOverlay";
// import { DisplayMode } from "@src/types/displayMode"

enum displayMode {
	CAMERA_ONLY = "camera_only",
	SCREEN_ONLY = "screen_only",
	CAMERA_SCREEN = "camera_screen"
}

interface Props {
	videoStream: MediaStream | undefined;
	audioStream: MediaStream | undefined;
	screenStream: MediaStream | undefined;

	isRecording: boolean;
	isPaused: boolean;

	recordMode: displayMode;
	isPortrait: boolean;

	teleprompterText: string;
	showText: boolean;
	fontSize: number;
	scriptSpeed: number;
}

const VideoTeleprompter: React.FC<Props> = ({
	videoStream,
	audioStream,
	screenStream,

	isRecording,
	isPaused,

	recordMode,
	isPortrait,

	teleprompterText,
	showText,
	fontSize,
	scriptSpeed,
}) => {
	const portraitStyle: React.CSSProperties = {
		width: "auto",
		height: "100%",
		aspectRatio: "9/16"
	};
	const landscapeStyle: React.CSSProperties = {
		width: "100%",
		height: "auto",
		aspectRatio: "16/9"
	};

	const canvasRef = useRef<HTMLCanvasElement | null>(null);
	const recorderRef = useRef<MediaRecorder>();
	const animationFrameRef = useRef<number>();

	const [finalStream, setFinalStream] = useState<MediaStream | undefined>();

	const [isMissingDevices, setIsMissingDevices] = useState(false);
	const isMissingDevicesRef = useRef(isMissingDevices);

	const [countdown, setCountdown] = useState<number | null>(null);
	const [isCountingDown, setIsCountingDown] = useState(false);

	const [scrollText, setScrollText] = useState(false);

	const combineStreams = async () => {
		// Cleanup before early return
		freeFinalStream();
		if (animationFrameRef.current) {
			cancelAnimationFrame(animationFrameRef.current);
		}

		if (!canvasRef.current || isMissingDevicesRef.current) {
			return;
		}

		const canvas = canvasRef.current;
		const ctx = canvas.getContext("2d");

		if (!ctx) {
			return;
		}

		let cameraVideo: HTMLVideoElement | undefined = undefined;
		if (recordMode !== displayMode.SCREEN_ONLY && videoStream) {
			cameraVideo = document.createElement("video");
			cameraVideo.srcObject = videoStream;
			cameraVideo.play();
		}

		let screenVideo: HTMLVideoElement | undefined = undefined;
		if (recordMode !== displayMode.CAMERA_ONLY && screenStream) {
			screenVideo = document.createElement("video");
			screenVideo.srcObject = screenStream;
			screenVideo.play();
		}

		ctx.fillStyle = "black";
		ctx.fillRect(0, 0, canvas.width, canvas.height);

		const drawFrame = () => {
			if (screenVideo) {
				drawScreenVideo(ctx, screenVideo);
			}
			if (cameraVideo) {
				drawCameraVideo(ctx, cameraVideo);
			}
			animationFrameRef.current = requestAnimationFrame(drawFrame);
		}
		drawFrame();

		//30 FPS
		const canvasStream = canvas.captureStream(30);

		if(audioStream){
			setFinalStream(new MediaStream([
				...canvasStream.getVideoTracks(),
				...audioStream.getAudioTracks(),
			]));
		} else {
			setFinalStream(new MediaStream([
				...canvasStream.getVideoTracks()
			]));
		}
		
	}

	const drawScreenVideo = (ctx: CanvasRenderingContext2D, screenVideo: HTMLVideoElement) => {
		const canvas = canvasRef.current;
		if (!canvas) {
			return;
		}

		const scale = canvas.width / screenVideo.videoWidth;
		const neededHeight = screenVideo.videoHeight * scale;
		ctx.drawImage(screenVideo,
			0, (canvas.height - neededHeight) / 2,
			canvas.width, neededHeight
		);
	}

	const drawCameraVideo = (ctx: CanvasRenderingContext2D, cameraVideo: HTMLVideoElement) => {
		const canvas = canvasRef.current;
		if (!canvas) {
			return;
		}

		if (recordMode === displayMode.CAMERA_SCREEN) {
			if (isPortrait) {
				const newHeight = canvas.height / 3;
				const newWidth = newHeight * 9 / 16;

				const neededWidth = cameraVideo.videoHeight * 9 / 16;
				const sx = (cameraVideo.videoWidth - neededWidth) / 2;

				ctx.drawImage(cameraVideo,
					sx, 0,
					neededWidth, cameraVideo.videoHeight,
					canvas.width - newWidth - 20, canvas.height - newHeight - 20,
					newWidth, newHeight
				);
			} else {
				const newWidth = canvas.width / 3;
				const newHeight = newWidth * 9 / 16;

				const neededHeight = cameraVideo.videoWidth * 9 / 16;
				const sy = (cameraVideo.videoHeight - neededHeight) / 2;

				ctx.drawImage(cameraVideo,
					0, sy,
					cameraVideo.videoWidth, neededHeight,
					canvas.width - newWidth - 20, canvas.height - newHeight - 20,
					newWidth, newHeight
				);
			}
		} else {
			if (isPortrait) {
				const neededWidth = cameraVideo.videoHeight * 9 / 16;
				const sx = (cameraVideo.videoWidth - neededWidth) / 2;

				ctx.drawImage(cameraVideo,
					sx, 0,
					neededWidth, cameraVideo.videoHeight,
					0, 0,
					canvas.width, canvas.height
				);
			} else {
				const neededHeight = cameraVideo.videoWidth * 9 / 16;
				const sy = (cameraVideo.videoHeight - neededHeight) / 2;

				ctx.drawImage(cameraVideo,
					0, sy,
					cameraVideo.videoWidth, neededHeight,
					0, 0,
					canvas.width, canvas.height
				);
			}
		}
	}

	const checkIfMissingDevices = () => {
		if(!audioStream) {
			return true;
		}
		if (recordMode === displayMode.CAMERA_ONLY && !videoStream) {
			return true;
		}
		if (recordMode === displayMode.SCREEN_ONLY && !screenStream) {
			return true;
		}
		if (recordMode === displayMode.CAMERA_SCREEN && (!videoStream || !screenStream)) {
			return true;
		}
		return false;
	}

	useEffect(() => {
		const missingDevices=checkIfMissingDevices();
		isMissingDevicesRef.current = missingDevices;
		setIsMissingDevices(missingDevices);

		combineStreams();
	}, [videoStream, audioStream, screenStream, recordMode, isPortrait]);

	useEffect(() => {
		return () => {
			if (animationFrameRef.current) {
				cancelAnimationFrame(animationFrameRef.current);
			}
			freeFinalStreamRef.current();
		};
	}, []);

	const startRecording = () => {
		if (!finalStream) {
			return;
		}
		
		recorderRef.current = new MediaRecorder(finalStream);
		let chunks: Blob[] = [];

		recorderRef.current.ondataavailable = (e) => chunks.push(e.data);
		recorderRef.current.onstop = () => {
			const blob = new Blob(chunks, { type: "video/webm" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = "recording.webm";
			a.click();
		};

		recorderRef.current.start();
	};

	useEffect(() => {
		if (!isCountingDown) {
			return;
		}

		const countdownInterval = setInterval(() => {
			setCountdown(prev => {
				if (prev === null || prev <= 1) {
					clearInterval(countdownInterval);
					setCountdown(null);
					setIsCountingDown(false);
					// Start recording after countdown
					startRecording();
					return null;
				}
				return prev - 1;
			});
		}, 1000);

		return () => {
			clearInterval(countdownInterval);
		};
	}, [isCountingDown])

	useEffect(() => {
		if (isRecording && !isCountingDown) {
			setCountdown(3);
			setIsCountingDown(true);
		} else if (!isRecording) {
			recorderRef.current?.stop();
			setCountdown(null);
			setIsCountingDown(false);
		}
	}, [isRecording]);

	useEffect(() => {
		if (isRecording && !isCountingDown) {
			if (isPaused) {
				recorderRef.current?.pause();
			} else {
				recorderRef.current?.resume();
			}
		}
	}, [isPaused]);

	useEffect(() => {
		if (isRecording && !isPaused) {
			setScrollText(true);
		} else {
			setScrollText(false);
		}
	}, [isRecording, isPaused]);

	const freeFinalStream = () => {
		freeMediaStream(finalStream);
		setFinalStream(undefined);
	};
	const freeFinalStreamRef = useRef(freeFinalStream);
	useEffect(() => {
		freeFinalStreamRef.current = freeFinalStream;
	}, [freeFinalStream]);
	
	return <>
		<div style={{ position: "relative", height:"419px", aspectRatio: (isPortrait ? "9/16" : "16/9"), margin: "auto" }}>
			{showText && (
				<TeleprompterTextOverlay
					teleprompterText={teleprompterText}
					fontSize={fontSize}
					scrollSpeed={scriptSpeed}
					scrollText={scrollText}
				/>
			)}
			{isMissingDevices && <MissingDevice recordMode={recordMode} />}
			{(countdown !== null && countdown > 0) && (
				<SemiTransparentOverlay>
					<WhiteText style={{ fontSize: "8rem" }}>{countdown}</WhiteText>
				</SemiTransparentOverlay>
			)}
			{/* {isSaving && (
				<SemiTransparentOverlay>
					<Flex style={{ flexDirection: "column", gap: "10px", width:"50%", textAlign: "center" }}>
						<WhiteText style={{ fontSize: "1.5rem" }}>Saving Video...</WhiteText>
						<StyledProgressBar style={{ margin: 0, height: "6px" }} now={savingProgress} />
					</Flex>
				</SemiTransparentOverlay>
			)} */}
			<canvas
				width={isPortrait ? 1080 : 1920}
				height={isPortrait ? 1920 : 1080}
				ref={canvasRef}
				style={{
					...(isPortrait ? portraitStyle : landscapeStyle),
					maxWidth: "100%", maxHeight: "100%", borderRadius: "10px",
					visibility: isMissingDevices ? "hidden" : "visible"
				}}
			/>
		</div>
	</>
};

export default VideoTeleprompter;
