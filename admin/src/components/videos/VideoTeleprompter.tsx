import { MainButton } from "@src/styles/components";
import React, { useEffect, useRef, useState } from "react";
import { freeMediaStream } from "../utils/freeMediaStream";
import MissingDevice from "../common/MissingDevice";
// import { DisplayMode } from "@src/types/displayMode"

enum displayMode {
	CAMERA_ONLY = "camera_only",
	SCREEN_ONLY = "screen_only",
	CAMERA_SCREEN = "camera_screen"
}

interface Props {
	videoStream: MediaStream | undefined;
	audioStream: MediaStream | undefined;
	screenStream: MediaStream | undefined;

	isRecording: boolean;
	isPaused: boolean;

	recordMode: displayMode;
	isPortrait: boolean;

	teleprompterText: string;
	showText: boolean;
	fontSize: number;
	scriptSpeed: number;

	isSaving: boolean;
}

const VideoTeleprompter: React.FC<Props> = ({
	videoStream,
	audioStream,
	screenStream,

	isRecording,
	isPaused,

	recordMode,
	isPortrait,

	teleprompterText,
	showText,
	fontSize,
	scriptSpeed,

	isSaving
}) => {
	const portraitStyle: React.CSSProperties = {
		width: "auto",
		height: "100%",
		aspectRatio: "9/16"
	};
	const landscapeStyle: React.CSSProperties = {
		width: "100%",
		height: "auto",
		aspectRatio: "16/9"
	};

	const canvasRef = useRef<HTMLCanvasElement | null>(null);
	const recorderRef = useRef<MediaRecorder>();
	const animationFrameRef = useRef<number>();

	const [finalStream, setFinalStream] = useState<MediaStream | undefined>();

	const [isMissingDevices, setIsMissingDevices] = useState(false);
	const isMissingDevicesRef = useRef(isMissingDevices);

	const combineStreams = async () => {
		// Cleanup before early return
		freeFinalStream();
		if (animationFrameRef.current) {
			cancelAnimationFrame(animationFrameRef.current);
		}

		if (!canvasRef.current || isMissingDevicesRef.current) {
			return;
		}

		const canvas = canvasRef.current;
		const ctx = canvas.getContext("2d");

		if (!ctx) {
			return;
		}

		let cameraVideo: HTMLVideoElement | undefined = undefined;
		if (recordMode !== displayMode.SCREEN_ONLY && videoStream) {
			cameraVideo = document.createElement("video");
			cameraVideo.srcObject = videoStream;
			cameraVideo.play();
		}

		let screenVideo: HTMLVideoElement | undefined = undefined;
		if (recordMode !== displayMode.CAMERA_ONLY && screenStream) {
			screenVideo = document.createElement("video");
			screenVideo.srcObject = screenStream;
			screenVideo.play();
		}

		ctx.fillStyle = "black";
		ctx.fillRect(0, 0, canvas.width, canvas.height);

		const drawFrame = () => {
			
			if (screenVideo) {
				const scale = canvas.width / screenVideo.videoWidth;
				const neededHeight = screenVideo.videoHeight * scale;
				ctx.drawImage(screenVideo, 
					0, (canvas.height - neededHeight) / 2, 
					canvas.width, neededHeight
				);
			}
			if (cameraVideo) {
				if (recordMode === displayMode.CAMERA_SCREEN) {
					if (isPortrait) {
						const newHeight = canvas.height / 3;
						const newWidth = newHeight * 9 / 16;

						const neededWidth = cameraVideo.videoHeight * 9 / 16;
						const sx = (cameraVideo.videoWidth - neededWidth) / 2;

						ctx.drawImage(cameraVideo,
							sx, 0,
							neededWidth, cameraVideo.videoHeight,
							canvas.width - newWidth-20, canvas.height - newHeight-20,
							newWidth, newHeight
						);
					} else {
						const newWidth = canvas.width / 3;
						const newHeight = newWidth * 9 / 16;

						const neededHeight = cameraVideo.videoWidth * 9 / 16;
						const sy = (cameraVideo.videoHeight - neededHeight) / 2;

						ctx.drawImage(cameraVideo,
							0, sy,
							cameraVideo.videoWidth, neededHeight,
							canvas.width-newWidth-20, canvas.height - newHeight-20,
							newWidth, newHeight
						);
					}
				} else {
					if (isPortrait) {
						const neededWidth = cameraVideo.videoHeight * 9 / 16;
						const sx = (cameraVideo.videoWidth - neededWidth) / 2;

						ctx.drawImage(cameraVideo,
							sx, 0,
							neededWidth, cameraVideo.videoHeight,
							0, 0,
							canvas.width, canvas.height
						);
					} else {
						const neededHeight = cameraVideo.videoWidth * 9 / 16;
						const sy=(cameraVideo.videoHeight - neededHeight) / 2;

						ctx.drawImage(cameraVideo,
							0, sy,
							cameraVideo.videoWidth, neededHeight,
							0, 0, 
							canvas.width, canvas.height
						);
					}
				}
			}
			animationFrameRef.current = requestAnimationFrame(drawFrame);
		}
		drawFrame();

		//30 FPS
		const canvasStream = canvas.captureStream(30);

		if(audioStream){
			setFinalStream(new MediaStream([
				...canvasStream.getVideoTracks(),
				...audioStream.getAudioTracks(),
			]));
		} else {
			setFinalStream(new MediaStream([
				...canvasStream.getVideoTracks()
			]));
		}
		
	}

	const checkIfMissingDevices = () => {
		if(!audioStream) {
			return true;
		}
		if (recordMode === displayMode.CAMERA_ONLY && !videoStream) {
			return true;
		}
		if (recordMode === displayMode.SCREEN_ONLY && !screenStream) {
			return true;
		}
		if (recordMode === displayMode.CAMERA_SCREEN && (!videoStream || !screenStream)) {
			return true;
		}
		return false;
	}

	useEffect(() => {
		const missingDevices=checkIfMissingDevices();
		isMissingDevicesRef.current = missingDevices;
		setIsMissingDevices(missingDevices);

		combineStreams();
	}, [videoStream, audioStream, screenStream, recordMode, isPortrait]);

	useEffect(() => {
		return () => {
			if (animationFrameRef.current) {
				cancelAnimationFrame(animationFrameRef.current);
			}
			freeFinalStreamRef.current();
		};
	}, []);

	const startRecording = () => {
		if (!finalStream) {
			return;
		}
		
		recorderRef.current = new MediaRecorder(finalStream);
		let chunks: Blob[] = [];

		recorderRef.current.ondataavailable = (e) => chunks.push(e.data);
		recorderRef.current.onstop = () => {
			const blob = new Blob(chunks, { type: "video/webm" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = "recording.webm";
			a.click();
		};

		recorderRef.current.start();
	};

	useEffect(() => {
		if (isRecording) {
			startRecording();
		} else {
			recorderRef.current?.stop();
		}
	}, [isRecording]);

	useEffect(() => {
		if (isRecording) {
			if (isPaused) {
				recorderRef.current?.pause();
			} else {
				recorderRef.current?.resume();
			}
		}
	}, [isPaused]);

	const freeFinalStream = () => {
		freeMediaStream(finalStream);
		setFinalStream(undefined);
	};
	const freeFinalStreamRef = useRef(freeFinalStream);
	useEffect(() => {
		freeFinalStreamRef.current = freeFinalStream;
	}, [freeFinalStream]);
	
	return <>
		<div style={{ position: "relative", height:"419px", aspectRatio: (isPortrait ? "9/16" : "16/9"), margin: "auto" }}>
			<div style={{
				position: "absolute",
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				top: 0,
				left: "50%",
				transform: "translateX(-50%)",
				margin: "10px 0",
				padding: "10px",
				boxSizing: "border-box"
			}}>
				<p style={{
					color: "white",
					top: 0,
					textAlign: "center",
					margin: 0
				}}>SAMPLE TELEPROMPT</p>
			</div>
			{ isMissingDevices && <MissingDevice recordMode={recordMode} /> }
			<canvas
				width={isPortrait ? 1080 : 1920}
				height={isPortrait ? 1920 : 1080}
				ref={canvasRef}
				style={{ ...(isPortrait ? portraitStyle : landscapeStyle), maxWidth: "100%", maxHeight: "100%", visibility: isMissingDevices ? "hidden" : "visible" }}
			/>
		</div>
	</>
};

export default VideoTeleprompter;
