import { MainButton } from "@src/styles/components";
import React, { useEffect, useRef, useState } from "react";
import { freeMediaStream } from "../utils/freeMediaStream";
// import { DisplayMode } from "@src/types/displayMode"

enum displayMode {
	CAMERA_ONLY = "camera_only",
	SCREEN_ONLY = "screen_only",
	CAMERA_SCREEN = "camera_screen"
}

interface Props {
	videoStream: MediaStream | undefined;
	audioStream: MediaStream | undefined;
	screenStream: MediaStream | undefined;

	isRecording: boolean;
	isPaused: boolean;
	hasStopped: boolean;

	displayMode: displayMode;
	isPortrait: boolean;

	teleprompterText: string;
	showText: boolean;
	fontSize: number;
	scriptSpeed: number;

	isSaving: boolean;
}

const VideoTeleprompter: React.FC<Props> = ({
	videoStream,
	audioStream,
	screenStream,

	isRecording,
	isPaused,
	hasStopped,

	displayMode,
	isPortrait,

	teleprompterText,
	showText,
	fontSize,
	scriptSpeed,

	isSaving
}) => {

	const canvasRef = useRef<HTMLCanvasElement | null>(null);
	const recorderRef = useRef<MediaRecorder>();
	const [finalStream, setFinalStream] = useState<MediaStream | undefined>();

	const [hasStarted, setHasStarted] = useState(false);

	async function combineStreams() {
		if (!canvasRef.current) {
			return;
		}

		const canvas = canvasRef.current;
		const ctx = canvas.getContext("2d");

		let cameraVideo: HTMLVideoElement | undefined = undefined;
		if (videoStream) {
			cameraVideo = document.createElement("video");
			cameraVideo.srcObject = videoStream;
			cameraVideo.play();
		}

		let screenVideo: HTMLVideoElement | undefined = undefined;
		if (screenStream) {
			screenVideo = document.createElement("video");
			screenVideo.srcObject = screenStream;
			screenVideo.play();
		}

		const drawFrame = () => {
			if (screenVideo) {
				ctx?.drawImage(screenVideo, 0, 0, canvas.width, canvas.height);
			}
			if (cameraVideo) {
				ctx?.drawImage(cameraVideo, 0, 0);
			}
			requestAnimationFrame(drawFrame);
		}
		drawFrame();

		const canvasStream = canvas.captureStream(30); // 30 FPS

		freeMediaStream(finalStream);

		if(audioStream){
			setFinalStream(new MediaStream([
				...canvasStream.getVideoTracks(),
				...audioStream.getAudioTracks(),
			]));
		} else {
			setFinalStream(new MediaStream([
				...canvasStream.getVideoTracks()
			]));
		}
		
	}

	useEffect(() => {
		console.log(4)
		combineStreams();
	}, [videoStream, audioStream, screenStream]);

	const startRecording = () => {
		if (!finalStream) {
			return;
		}
		
		recorderRef.current = new MediaRecorder(finalStream);
		let chunks: Blob[] = [];

		recorderRef.current.ondataavailable = (e) => chunks.push(e.data);
		recorderRef.current.onstop = () => {
			const blob = new Blob(chunks, { type: "video/webm" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = "recording.webm";
			a.click();
		};

		recorderRef.current.start();
	};

	const startRecordingRef = useRef(startRecording);

	useEffect(() => {
		startRecordingRef.current = startRecording;
	}, [startRecording]);

	const pauseRecording = () => {
		mediaRecorder.pause();
	};

	const resumeRecording = () => {
		mediaRecorder.resume();
	};

	const stopRecording = () => {
		mediaRecorder.stop();
	};

	useEffect(() => {

	}, [isRecording]);

	useEffect(() => {

	}, [isPaused]);

	useEffect(() => {

	}, [hasStopped]);
	
	return <>
		<div style={{ position:"relative"}}>
			<MainButton onClick={()=>{
				startRecording();
				}}>START</MainButton>
			<MainButton onClick={() => {
				recorderRef.current?.pause();
			}}>PAUSE</MainButton>
			<MainButton onClick={() => {
				recorderRef.current?.resume();
			}}>RESUME</MainButton>
			<MainButton onClick={() => {
				recorderRef.current?.stop();
			}}>STOP</MainButton>
			<p style={{position: "absolute", color: "white", top:100, left: 100}}>LOOK AT ME IM ON TOP OF THE CANVAS</p>
			<canvas width={750} height={375} ref={canvasRef} style={{width:"100%", height:"100%", aspectRatio: "16/9"}}/>
		</div>
	</>
};

export default VideoTeleprompter;
