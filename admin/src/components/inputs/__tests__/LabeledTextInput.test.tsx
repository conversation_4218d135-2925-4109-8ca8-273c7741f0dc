import "@testing-library/jest-dom";
import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import LabeledTextInput from "../LabeledTextInput";
import { ThemeProvider } from "styled-components";
import apTheme from "@src/config/theme";

const renderWithTheme = (ui: React.ReactElement) =>
	render(<ThemeProvider theme={apTheme}>{ui}</ThemeProvider>);

describe("LabeledTextInput Component", () => {
	const mockOnChange = jest.fn();

	beforeEach(() => {
		mockOnChange.mockClear();
	});

	it("renders with provided label and value", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Test Label"
				value="test value"
				onChange={mockOnChange}
			/>
		);

		expect(screen.getByTestId("labeledTextInputLabel")).toBeInTheDocument();
		expect(screen.getByTestId("labeledTextInputLabel")).toHaveTextContent("Test Label");

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		expect(input).toBeInTheDocument();
		expect(input.value).toBe("test value");
	});

	it("uses default placeholder from translation", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Test Label"
				value=""
				onChange={mockOnChange}
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		expect(input.placeholder).toBe("");
	});

	it("uses custom placeholder when provided", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Test Label"
				value=""
				onChange={mockOnChange}
				placeholder="Custom placeholder"
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		expect(input.placeholder).toBe("Custom placeholder");
	});

	it("calls onChange when input value changes", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Test Label"
				value=""
				onChange={mockOnChange}
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		fireEvent.change(input, { target: { value: "new value" } });

		expect(mockOnChange).toHaveBeenCalledWith("new value");
		expect(mockOnChange).toHaveBeenCalledTimes(1);
	});

	it("renders with empty string value", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Empty Test"
				value=""
				onChange={mockOnChange}
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		expect(input.value).toBe("");
	});

	it("handles special characters in input", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Special Test"
				value=""
				onChange={mockOnChange}
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		fireEvent.change(input, { target: { value: "Test @#$% & special chars!" } });

		expect(mockOnChange).toHaveBeenCalledWith("Test @#$% & special chars!");
	});

	it("has correct input attributes", () => {
		renderWithTheme(
			<LabeledTextInput
				label="Attributes Test"
				value="test"
				onChange={mockOnChange}
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		expect(input).toHaveAttribute("type", "text");
		expect(input).toHaveAttribute("id", "labeledTextInput");
		expect(input).toHaveClass("form-control");
	});

	it("updates correctly when value prop changes", () => {
		const { rerender } = renderWithTheme(
			<LabeledTextInput
				label="Update Test"
				value="initial"
				onChange={mockOnChange}
			/>
		);

		const input = screen.getByTestId("labeledTextInput") as HTMLInputElement;
		expect(input.value).toBe("initial");

		rerender(
			<ThemeProvider theme={apTheme}>
				<LabeledTextInput
					label="Update Test"
					value="updated"
					onChange={mockOnChange}
				/>
			</ThemeProvider>
		);

		expect(input.value).toBe("updated");
	});
});
