import React, { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "../hooks/translations";
import { Flex, InlineIcon, PageSection, SmallSelectButton, SmallSelectDiv } from "@src/styles/components";
import { SubheadingText } from "@src/styles/forms";
import DeviceDropdown from "../common/DeviceDropdown";
import { HelpIcon, MicIcon } from "@src/assets";
import SVG from "../svg/SVG";

interface Props {
	disabled: boolean;
	videoStream: MediaStream | undefined;
	setVideoStream: (stream: MediaStream | undefined) => void;
	audioStream: MediaStream | undefined;
	setAudioStream: (stream: MediaStream | undefined) => void;
	screenStream: MediaStream | undefined;
	setScreenStream: (stream: MediaStream | undefined) => void;
}

const InputDevicesSelect: React.FC<Props> = ({ disabled, videoStream, setVideoStream, audioStream, setAudioStream, screenStream, setScreenStream }) => {

	const translation = useTranslation();

	const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([]);
	const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);

	const [selectedVideoDeviceId, setSelectedVideoDeviceId] = useState<string>();
	const [selectedAudioDeviceId, setSelectedAudioDeviceId] = useState<string>();
	const [selectedScreenDeviceName, setSelectedScreenDeviceName] = useState<string>();

	const requestVideoDevices = () => {
		let permissionStream: MediaStream | undefined = undefined;

		navigator.mediaDevices.getUserMedia({ video: true })
			.then((s) => {
				permissionStream = s;
				return navigator.mediaDevices.enumerateDevices();
			})
			.then((devices) => {
				const cameras = devices.filter((d) => d.kind === "videoinput");

				if (cameras.length > 0 && cameras[0].deviceId) {
					setVideoDevices(cameras);
					setSelectedVideoDeviceId(cameras[0].deviceId);
				}
			})
			.catch(() => {
				setVideoDevices([]);
			})
			.finally(() => {
				if (permissionStream) {
					permissionStream.getTracks().forEach(t => t.stop());
					permissionStream = undefined;
				}
			});
	};

	const requestAudioDevices = () => {
		let permissionStream: MediaStream | undefined = undefined;

		navigator.mediaDevices.getUserMedia({ audio: true })
			.then((s) => {
				permissionStream = s;
				return navigator.mediaDevices.enumerateDevices();
			})
			.then((devices) => {
				const mics = devices.filter((d) => d.kind === "audioinput");

				if (mics.length > 0 && mics[0].deviceId) {
					setAudioDevices(mics);
					setSelectedAudioDeviceId(mics[0].deviceId);
				}
			})
			.catch(() => {
				setAudioDevices([]);
			})
			.finally(() => {
				if (permissionStream) {
					permissionStream.getTracks().forEach(t => t.stop());
					permissionStream = undefined;
				}
			});
	};

	const requestScreenDevice = () => {
		let newScreenStream: MediaStream | undefined = undefined;

		freeScreenStream();

		navigator.mediaDevices.getDisplayMedia({ video: true })
			.then((s) => {
				newScreenStream = s;
				setScreenStream(newScreenStream);
				setSelectedScreenDeviceName(formatScreenLabel(newScreenStream.getVideoTracks()[0].label));

				// Detect when user clicks stops sharing
				s.getVideoTracks()[0].addEventListener("ended", () => {
					freeScreenStreamRef.current();
				});
			})
			.catch((error) => {
				console.error("Screen share error:", error);
			});
	};

	/**
	 * Browser capabilities and label outputs:
	 * Chrome:
	 	* Screen: "screen:x:y"
		* Window: "window:x:y"
		* Tab: "web-contents-media-stream://xyzjargain"
	 * FireFox:
	 	* Screen: "Primary Monitor"
		* Window: "Primary Monitor"
	 * Safari:
	 	* Screen: "Screen "
		* Window: Actual name of window
	**/
	const formatScreenLabel = (label: string): string => {
		if (label.toLowerCase().includes("screen")) {
			return "Screen";
		} else if (label.toLowerCase().includes("web-contents-media-stream")) {
			return "Tab";
		}
		return "Window";
	};

	const freeStream = (stream: MediaStream | undefined) => {
		if (stream) {
			stream.getTracks().forEach(t => t.stop());
		}
	};

	const freeVideoStream = useCallback(() => {
		if (videoStream) {
			setSelectedVideoDeviceId(undefined);
			freeStream(videoStream);
			setVideoStream(undefined);
		}
	}, [videoStream, setVideoStream]);

	const freeAudioStream = useCallback(() => {
		if (audioStream) {
			setSelectedAudioDeviceId(undefined);
			freeStream(audioStream);
			setAudioStream(undefined);
		}
	}, [audioStream, setAudioStream]);

	const freeScreenStream = useCallback(() => {
		if (screenStream) {
			setSelectedScreenDeviceName(undefined);
			freeStream(screenStream);
			setScreenStream(undefined);
		}
	}, [screenStream, setScreenStream]);

	//Cleanup on dismount
	const freeVideoStreamRef = useRef(freeVideoStream);
	const freeAudioStreamRef = useRef(freeAudioStream);
	const freeScreenStreamRef = useRef(freeScreenStream);

	useEffect(() => {
		freeVideoStreamRef.current = freeVideoStream;
	}, [freeVideoStream]);

	useEffect(() => {
		freeAudioStreamRef.current = freeAudioStream;
	}, [freeAudioStream]);

	useEffect(() => {
		freeScreenStreamRef.current = freeScreenStream;
	}, [freeScreenStream]);

	useEffect(() => {
		return () => {
			freeVideoStreamRef.current();
			freeAudioStreamRef.current();
			freeScreenStreamRef.current();
		};
	}, []);

	useEffect(() => {
		requestVideoDevices();
		requestAudioDevices();
	}, []);

	useEffect(() => {
		const updateVideoStream = async () => {
			freeStream(videoStream);
			if (selectedVideoDeviceId) {
				const constraints = { video: { deviceId: selectedVideoDeviceId } };
				const stream = await navigator.mediaDevices.getUserMedia(constraints);
				setVideoStream(stream);
			} else {
				setVideoStream(undefined);
			}
		};

		updateVideoStream();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedVideoDeviceId, setVideoStream]);

	useEffect(() => {
		const updateAudioStream = async () => {
			freeStream(audioStream);
			if (selectedAudioDeviceId) {
				const constraints = { audio: { deviceId: selectedAudioDeviceId } };
				const stream = await navigator.mediaDevices.getUserMedia(constraints);
				setAudioStream(stream);
			} else {
				setAudioStream(undefined);
			}
		};

		updateAudioStream();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedAudioDeviceId, setAudioStream]);

	return (
		<>
			<div style={{
				cursor: (disabled) ? "not-allowed" : ""
			}}>
				<div
					style={
						(disabled)
							? { opacity: 0.5, pointerEvents: "none" }
							: {}
					}
				>
					<SubheadingText style={{ fontWeight: "bold" }}>
						{translation.general.inputDevices}
					</SubheadingText>
					<PageSection>
						<Flex style={{ flexDirection: "column", gap: "15px" }}>
							<div>
								<Flex style={{ gap: "5px", alignItems: "center", marginBottom: "5px", height: "1rem" }}>
									<InlineIcon>
										<SVG src={HelpIcon} />
									</InlineIcon>
									<SubheadingText style={{ fontSize: "0.75rem", fontWeight: "bold" }}>
										{translation.general.camera}
									</SubheadingText>
								</Flex>
								<DeviceDropdown
									id="camera-select"
									value={selectedVideoDeviceId}
									setValue={setSelectedVideoDeviceId}
									devices={videoDevices}
									disabled={disabled}
								/>
							</div>
							<div>
								<Flex style={{ gap: "5px", alignItems: "center", marginBottom: "5px", height: "1rem" }}>
									<InlineIcon>
										<SVG src={MicIcon} />
									</InlineIcon>
									<SubheadingText style={{ fontSize: "0.75rem", fontWeight: "bold" }}>
										{translation.general.microphone}
									</SubheadingText>
								</Flex>
								<DeviceDropdown
									id="mic-select"
									value={selectedAudioDeviceId}
									setValue={setSelectedAudioDeviceId}
									devices={audioDevices}
									disabled={disabled}
								/>
							</div>
							<div>
								<Flex style={{ gap: "5px", alignItems: "center", marginBottom: "5px", height: "1rem" }}>
									<InlineIcon>
										<SVG src={HelpIcon} />
									</InlineIcon>
									<SubheadingText style={{ fontSize: "0.75rem", fontWeight: "bold" }}>
										{translation.general.screen}
									</SubheadingText>
								</Flex>
								<Flex style={{ gap: "5px" }}>
									<SmallSelectDiv id="screen-select" style={{ flex: 3 }}>
										{selectedScreenDeviceName ?? "-"}
									</SmallSelectDiv>
									<SmallSelectButton
										id={screenStream ? "select-screen" : "stop-sharing-screen"}
										onClick={screenStream ? freeScreenStream : requestScreenDevice}
										style={{ flex: 2 }}
									>
										{screenStream ? translation.general.stopSharing : translation.general.select}
									</SmallSelectButton>
								</Flex>
							</div>
						</Flex>
					</PageSection>
				</div>
			</div>
		</>
	);
};

export default InputDevicesSelect;
