import React from "react";
import { Form } from "react-bootstrap";
import { VideoPageTitleText } from "@src/styles/components";
import { CustomInput } from "@src/styles/forms";

interface Props {
	label: string;
	value: string;
	onChange: (value: string) => void;
	placeholder?: string;
}

const LabeledTextInput: React.FC<Props> = ({
	label,
	value,
	onChange,
	placeholder
}) => {
	return (
		<>
			<VideoPageTitleText data-testid="labeledTextInputLabel" className="mb-3">
				{label}
			</VideoPageTitleText>
			<Form.Group className="mb-3">
				<CustomInput
					className="form-control"
					type="text"
					placeholder={placeholder}
					id="labeledTextInput"
					data-testid="labeledTextInput"
					onChange={(e) => onChange(e.target.value)}
					value={value}
				/>
			</Form.Group>
		</>
	);
};

export default LabeledTextInput;
