import React from "react";
import { Flex, WhiteText } from "@src/styles/components";
import { useTranslation } from "../hooks/translations";

enum displayMode {
	CAMERA_ONLY = "camera_only",
	SCREEN_ONLY = "screen_only",
	CAMERA_SCREEN = "camera_screen"
}

interface Props {
	recordMode: displayMode;
}

const MissingDevice: React.FC<Props> = ({recordMode}) => {

	const translation = useTranslation();

	const getMissingDeviceMessage = () => {
		switch (recordMode) {
			case displayMode.CAMERA_ONLY:
				return translation.recordVideoPage.enableCameraMic;
			case displayMode.SCREEN_ONLY:
				return translation.recordVideoPage.enableScreenMic;
			case displayMode.CAMERA_SCREEN:
				return translation.recordVideoPage.enableCameraMicScreen;
		}
	};

	return (
		<div style={{ backgroundColor: "black", width: "100%", height: "100%", borderRadius: "10px" }}>
			<Flex style={{
				flexDirection: "column",
				justifyContent: "center",
				alignItems: "center",
				height: "100%",
				gap: "10px",
				textAlign: "center",
				margin: "0 10px"
			}}>
				<WhiteText style={{ fontSize: "1.2rem" }}>{ getMissingDeviceMessage() }</WhiteText>
				<WhiteText style={{ fontSize: "0.7rem" }}>{ translation.recordVideoPage.pleaseAllowAccess }</WhiteText>
			</Flex>
		</div>
	);
};

export default MissingDevice;
